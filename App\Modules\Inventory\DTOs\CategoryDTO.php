<?php

declare(strict_types=1);

namespace App\Modules\Inventory\DTOs;

use App\Core\Abstracts\BaseDTO;

class CategoryDTO extends BaseDTO {
   public function __construct(
      public ?int $id = null,
      public ?string $code = null,
      public ?string $image_path = null,
      public int $is_active = 1,
      public int $sort_order = 1,
      public int $parent_id = 0,
      public int $group_id = 1,
      public ?array $translate = null
   ) {
   }
}
