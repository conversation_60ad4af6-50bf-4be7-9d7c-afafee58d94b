<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class ManufacturerRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'manufacturer'
   ) {
      $this->database->prefix('inv_');
   }

   public function findAll(int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_manufacturer.*,
               inv_manufacturer_translate.title,
               inv_manufacturer_translate.content
            FROM inv_manufacturer

            LEFT JOIN inv_manufacturer_translate ON inv_manufacturer_translate.manufacturer_id = inv_manufacturer.id
               AND inv_manufacturer_translate.language_id = :language_id
            WHERE inv_manufacturer.deleted_at IS NULL
         ')
         ->execute([
            'language_id' => $lang_id,
         ])
         ->fetchAll();
   }

   public function findCompeting(int $lang_id, int $is_competing): array {
      return $this->database
         ->prepare('SELECT
               inv_manufacturer.*,
               inv_manufacturer_translate.title,
               inv_manufacturer_translate.content
            FROM inv_manufacturer

            LEFT JOIN inv_manufacturer_translate ON inv_manufacturer_translate.manufacturer_id = inv_manufacturer.id
               AND inv_manufacturer_translate.language_id = :language_id
            WHERE inv_manufacturer.deleted_at IS NULL
               AND inv_manufacturer.is_competing = :is_competing
         ')
         ->execute([
            'language_id' => $lang_id,
            'is_competing' => $is_competing
         ])
         ->fetchAll();
   }

   public function findOne(int $id, int $lang_id): array|false {
      return $this->database
         ->prepare('SELECT
               inv_manufacturer.*,
               COALESCE(inv_manufacturer_translate.title, default_translate.title) AS `title`,
               COALESCE(inv_manufacturer_translate.content, default_translate.content) AS `content`
            FROM inv_manufacturer

            LEFT JOIN inv_manufacturer_translate ON inv_manufacturer_translate.manufacturer_id = inv_manufacturer.id
               AND inv_manufacturer_translate.language_id = :language_id
            LEFT JOIN inv_manufacturer_translate AS default_translate ON default_translate.manufacturer_id = inv_manufacturer.id
               AND default_translate.language_id = 1
            WHERE inv_manufacturer.deleted_at IS NULL
               AND inv_manufacturer.id = :id
         ')
         ->execute([
            'id' => $id,
            'language_id' => $lang_id,
         ])
         ->fetch();
   }
}
