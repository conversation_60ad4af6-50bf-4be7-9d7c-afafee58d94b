<?php

declare(strict_types=1);

namespace App\Modules\Website\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class ProductRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'product'
   ) {
      // Prefix kullanmıyoruz çünkü hem inv_ hem web_ tablolarını kullanacağız
   }

   public function findAll(int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_product.id,
               COALESCE(web_product.code, inv_product.code) AS code,
               COALESCE(web_product.is_active, inv_product.is_active) AS is_active,
               COALESCE(web_product.sort_order, inv_product.sort_order) AS sort_order,
               COALESCE(web_product.created_at, inv_product.created_at) AS created_at,
               COALESCE(web_product.updated_at, inv_product.updated_at) AS updated_at,
               COALESCE(web_product.deleted_at, inv_product.deleted_at) AS deleted_at,
               COALESCE(web_product_translate.title, inv_product_translate.title) AS title,
               COALESCE(web_product_translate.content, inv_product_translate.content) AS content,
               web_product_translate.url,
               web_product_translate.meta_title,
               web_product_translate.meta_description,
               web_product_translate.meta_keywords
            FROM inv_product

            LEFT JOIN web_product ON web_product.id = inv_product.id
               AND web_product.deleted_at IS NULL
            LEFT JOIN inv_product_translate ON inv_product_translate.product_id = inv_product.id
               AND inv_product_translate.language_id = :language_id1
            LEFT JOIN web_product_translate ON web_product_translate.product_id = inv_product.id
               AND web_product_translate.language_id = :language_id2
            WHERE inv_product.deleted_at IS NULL
               AND COALESCE(web_product.is_active, inv_product.is_active) = 1
            ORDER BY COALESCE(web_product.sort_order, inv_product.sort_order) ASC,
                     COALESCE(web_product.created_at, inv_product.created_at) DESC
         ')
         ->execute([
            'language_id1' => $lang_id,
            'language_id2' => $lang_id
         ])
         ->fetchAll();
   }

   public function findOne(int $id, int $lang_id): array|false {
      return $this->database
         ->prepare('SELECT
               inv_product.id,
               COALESCE(web_product.code, inv_product.code) AS code,
               COALESCE(web_product.is_active, inv_product.is_active) AS is_active,
               COALESCE(web_product.sort_order, inv_product.sort_order) AS sort_order,
               COALESCE(web_product.created_at, inv_product.created_at) AS created_at,
               COALESCE(web_product.updated_at, inv_product.updated_at) AS updated_at,
               COALESCE(web_product.deleted_at, inv_product.deleted_at) AS deleted_at,
               COALESCE(
                  COALESCE(web_product_translate.title, web_default_translate.title),
                  COALESCE(inv_product_translate.title, inv_default_translate.title)
               ) AS title,
               COALESCE(
                  COALESCE(web_product_translate.content, web_default_translate.content),
                  COALESCE(inv_product_translate.content, inv_default_translate.content)
               ) AS content,
               COALESCE(web_product_translate.url, web_default_translate.url) AS url,
               COALESCE(web_product_translate.meta_title, web_default_translate.meta_title) AS meta_title,
               COALESCE(web_product_translate.meta_description, web_default_translate.meta_description) AS meta_description,
               COALESCE(web_product_translate.meta_keywords, web_default_translate.meta_keywords) AS meta_keywords
            FROM inv_product

            LEFT JOIN web_product ON web_product.id = inv_product.id
               AND web_product.deleted_at IS NULL
            LEFT JOIN inv_product_translate ON inv_product_translate.product_id = inv_product.id
               AND inv_product_translate.language_id = :language_id
            LEFT JOIN inv_product_translate AS inv_default_translate ON inv_default_translate.product_id = inv_product.id
               AND inv_default_translate.language_id = 1
            LEFT JOIN web_product_translate ON web_product_translate.product_id = inv_product.id
               AND web_product_translate.language_id = :language_id
            LEFT JOIN web_product_translate AS web_default_translate ON web_default_translate.product_id = inv_product.id
               AND web_default_translate.language_id = 1
            WHERE inv_product.deleted_at IS NULL
               AND inv_product.id = :id
         ')
         ->execute([
            'id' => $id,
            'language_id' => $lang_id,
         ])
         ->fetch();
   }

   public function findByUrl(string $url, int $lang_id): array|false {
      return $this->database
         ->prepare('SELECT
               inv_product.id,
               COALESCE(web_product.code, inv_product.code) AS code,
               COALESCE(web_product.is_active, inv_product.is_active) AS is_active,
               COALESCE(web_product.sort_order, inv_product.sort_order) AS sort_order,
               COALESCE(web_product.created_at, inv_product.created_at) AS created_at,
               COALESCE(web_product.updated_at, inv_product.updated_at) AS updated_at,
               COALESCE(web_product.deleted_at, inv_product.deleted_at) AS deleted_at,
               COALESCE(web_product_translate.title, inv_product_translate.title) AS title,
               COALESCE(web_product_translate.content, inv_product_translate.content) AS content,
               web_product_translate.url,
               web_product_translate.meta_title,
               web_product_translate.meta_description,
               web_product_translate.meta_keywords
            FROM inv_product

            LEFT JOIN web_product ON web_product.id = inv_product.id
               AND web_product.deleted_at IS NULL
            LEFT JOIN inv_product_translate ON inv_product_translate.product_id = inv_product.id
               AND inv_product_translate.language_id = :language_id
            JOIN web_product_translate ON web_product_translate.product_id = inv_product.id
               AND web_product_translate.language_id = :language_id
               AND web_product_translate.url = :url
            WHERE inv_product.deleted_at IS NULL
               AND COALESCE(web_product.is_active, inv_product.is_active) = 1
         ')
         ->execute([
            'url' => $url,
            'language_id' => $lang_id,
         ])
         ->fetch();
   }

   public function findCategory(int $product_id, int $lang_id): array {
      // Önce web_product_category'den kontrol et, yoksa inv_product_category'den al
      $webCategories = $this->database
         ->prepare('SELECT
               web_category.id,
               COALESCE(web_category_translate.title, inv_category_translate.title) AS title,
               web_category_translate.url
            FROM web_product_category

            JOIN web_category ON web_category.id = web_product_category.category_id
               AND web_category.deleted_at IS NULL
               AND web_category.is_active = 1
            LEFT JOIN web_category_translate ON web_category_translate.category_id = web_product_category.category_id
               AND web_category_translate.language_id = :language_id
            LEFT JOIN inv_category_translate ON inv_category_translate.category_id = web_product_category.category_id
               AND inv_category_translate.language_id = :language_id
            WHERE web_product_category.product_id = :product_id
            ORDER BY web_category.sort_order ASC
         ')
         ->execute([
            'product_id' => $product_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();

      // Eğer web'de kategori yoksa inventory'den al
      if (empty($webCategories)) {
         return $this->database
            ->prepare('SELECT
                  inv_category.id,
                  inv_category_translate.title,
                  NULL as url
               FROM inv_product_category

               JOIN inv_category ON inv_category.id = inv_product_category.category_id
                  AND inv_category.deleted_at IS NULL
               LEFT JOIN inv_category_translate ON inv_category_translate.category_id = inv_product_category.category_id
                  AND inv_category_translate.language_id = :language_id
               WHERE inv_product_category.product_id = :product_id
               ORDER BY inv_category.sort_order ASC
            ')
            ->execute([
               'product_id' => $product_id,
               'language_id' => $lang_id,
            ])
            ->fetchAll();
      }

      return $webCategories;
   }

   public function findManufacturer(int $product_id, int $lang_id): array {
      // Önce web_product_manufacturer'den kontrol et, yoksa inv_product_manufacturer'den al
      $webManufacturers = $this->database
         ->prepare('SELECT
               web_manufacturer.id,
               COALESCE(web_manufacturer_translate.title, inv_manufacturer_translate.title) AS title,
               web_manufacturer.image_path
            FROM web_product_manufacturer

            JOIN web_manufacturer ON web_manufacturer.id = web_product_manufacturer.manufacturer_id
               AND web_manufacturer.deleted_at IS NULL
               AND web_manufacturer.is_active = 1
            LEFT JOIN web_manufacturer_translate ON web_manufacturer_translate.manufacturer_id = web_product_manufacturer.manufacturer_id
               AND web_manufacturer_translate.language_id = :language_id
            LEFT JOIN inv_manufacturer_translate ON inv_manufacturer_translate.manufacturer_id = web_product_manufacturer.manufacturer_id
               AND inv_manufacturer_translate.language_id = :language_id
            WHERE web_product_manufacturer.product_id = :product_id
            ORDER BY web_manufacturer.sort_order ASC
         ')
         ->execute([
            'product_id' => $product_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();

      // Eğer web'de manufacturer yoksa inventory'den al
      if (empty($webManufacturers)) {
         return $this->database
            ->prepare('SELECT
                  inv_manufacturer.id,
                  inv_manufacturer_translate.title,
                  inv_manufacturer.image_path
               FROM inv_product_manufacturer

               JOIN inv_manufacturer ON inv_manufacturer.id = inv_product_manufacturer.manufacturer_id
                  AND inv_manufacturer.deleted_at IS NULL
                  AND inv_manufacturer.is_active = 1
               LEFT JOIN inv_manufacturer_translate ON inv_manufacturer_translate.manufacturer_id = inv_product_manufacturer.manufacturer_id
                  AND inv_manufacturer_translate.language_id = :language_id
               WHERE inv_product_manufacturer.product_id = :product_id
               ORDER BY inv_manufacturer.sort_order ASC
            ')
            ->execute([
               'product_id' => $product_id,
               'language_id' => $lang_id,
            ])
            ->fetchAll();
      }

      return $webManufacturers;
   }

   public function findAttr(int $product_id, int $lang_id): array {
      // Önce web_product_attr'den kontrol et, yoksa inv_product_attr'den al
      $webAttrs = $this->database
         ->prepare('SELECT
               web_attr.id,
               COALESCE(web_attr_translate.title, inv_attr_translate.title) AS title
            FROM web_product_attr

            JOIN web_attr ON web_attr.id = web_product_attr.attr_id
               AND web_attr.deleted_at IS NULL
            LEFT JOIN web_attr_translate ON web_attr_translate.attr_id = web_product_attr.attr_id
               AND web_attr_translate.language_id = :language_id
            LEFT JOIN inv_attr_translate ON inv_attr_translate.attr_id = web_product_attr.attr_id
               AND inv_attr_translate.language_id = :language_id
            WHERE web_product_attr.product_id = :product_id
            ORDER BY web_attr.sort_order ASC
         ')
         ->execute([
            'product_id' => $product_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();

      // Eğer web'de attr yoksa inventory'den al
      if (empty($webAttrs)) {
         return $this->database
            ->prepare('SELECT
                  inv_attr.id,
                  inv_attr_translate.title
               FROM inv_product_attr

               JOIN inv_attr ON inv_attr.id = inv_product_attr.attr_id
                  AND inv_attr.deleted_at IS NULL
               LEFT JOIN inv_attr_translate ON inv_attr_translate.attr_id = inv_product_attr.attr_id
                  AND inv_attr_translate.language_id = :language_id
               WHERE inv_product_attr.product_id = :product_id
               ORDER BY inv_attr.sort_order ASC
            ')
            ->execute([
               'product_id' => $product_id,
               'language_id' => $lang_id,
            ])
            ->fetchAll();
      }

      return $webAttrs;
   }

   public function findStandard(int $product_id, int $lang_id): array {
      // Önce web_product_standard'den kontrol et, yoksa inv_product_standard'den al
      $webStandards = $this->database
         ->prepare('SELECT
               web_standard.id,
               COALESCE(web_standard_translate.title, inv_standard_translate.title) AS title,
               web_product_standard.value,
               web_standard.image_path
            FROM web_product_standard

            JOIN web_standard ON web_standard.id = web_product_standard.standard_id
               AND web_standard.deleted_at IS NULL
            LEFT JOIN web_standard_translate ON web_standard_translate.standard_id = web_product_standard.standard_id
               AND web_standard_translate.language_id = :language_id
            LEFT JOIN inv_standard_translate ON inv_standard_translate.standard_id = web_product_standard.standard_id
               AND inv_standard_translate.language_id = :language_id
            WHERE web_product_standard.product_id = :product_id
            ORDER BY web_standard.sort_order ASC
         ')
         ->execute([
            'product_id' => $product_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();

      // Eğer web'de standard yoksa inventory'den al
      if (empty($webStandards)) {
         return $this->database
            ->prepare('SELECT
                  inv_standard.id,
                  inv_standard_translate.title,
                  inv_product_standard.value,
                  inv_standard.image_path
               FROM inv_product_standard

               JOIN inv_standard ON inv_standard.id = inv_product_standard.standard_id
                  AND inv_standard.deleted_at IS NULL
               LEFT JOIN inv_standard_translate ON inv_standard_translate.standard_id = inv_product_standard.standard_id
                  AND inv_standard_translate.language_id = :language_id
               WHERE inv_product_standard.product_id = :product_id
               ORDER BY inv_standard.sort_order ASC
            ')
            ->execute([
               'product_id' => $product_id,
               'language_id' => $lang_id,
            ])
            ->fetchAll();
      }

      return $webStandards;
   }

   public function findImage(int $product_id): array {
      // Önce web_product_image'den kontrol et, yoksa inv_product_image'den al
      $webImages = $this->database
         ->prepare('SELECT
               web_product_image.id,
               web_product_image.product_id,
               web_product_image.image_path
            FROM web_product_image
            WHERE web_product_image.deleted_at IS NULL
               AND web_product_image.image_path IS NOT NULL
               AND web_product_image.product_id = :product_id
            ORDER BY web_product_image.sort_order ASC, web_product_image.created_at ASC
         ')
         ->execute([
            'product_id' => $product_id,
         ])
         ->fetchAll();

      // Eğer web'de image yoksa inventory'den al
      if (empty($webImages)) {
         return $this->database
            ->prepare('SELECT
                  inv_product_image.id,
                  inv_product_image.product_id,
                  inv_product_image.image_path
               FROM inv_product_image
               WHERE inv_product_image.deleted_at IS NULL
                  AND inv_product_image.image_path IS NOT NULL
                  AND inv_product_image.product_id = :product_id
               ORDER BY inv_product_image.sort_order ASC, inv_product_image.created_at ASC
            ')
            ->execute([
               'product_id' => $product_id,
            ])
            ->fetchAll();
      }

      return $webImages;
   }

   public function findByCategoryId(int $category_id, int $lang_id, int $limit = 10, int $offset = 0): array {
      return $this->database
         ->prepare('SELECT
               inv_product.id,
               COALESCE(web_product.code, inv_product.code) AS code,
               COALESCE(web_product.is_active, inv_product.is_active) AS is_active,
               COALESCE(web_product.sort_order, inv_product.sort_order) AS sort_order,
               COALESCE(web_product.created_at, inv_product.created_at) AS created_at,
               COALESCE(web_product.updated_at, inv_product.updated_at) AS updated_at,
               COALESCE(web_product.deleted_at, inv_product.deleted_at) AS deleted_at,
               COALESCE(web_product_translate.title, inv_product_translate.title) AS title,
               COALESCE(web_product_translate.content, inv_product_translate.content) AS content,
               web_product_translate.url
            FROM inv_product

            LEFT JOIN web_product ON web_product.id = inv_product.id
               AND web_product.deleted_at IS NULL
            LEFT JOIN inv_product_translate ON inv_product_translate.product_id = inv_product.id
               AND inv_product_translate.language_id = :language_id
            LEFT JOIN web_product_translate ON web_product_translate.product_id = inv_product.id
               AND web_product_translate.language_id = :language_id
            LEFT JOIN web_product_category ON web_product_category.product_id = inv_product.id
            LEFT JOIN inv_product_category ON inv_product_category.product_id = inv_product.id
            WHERE inv_product.deleted_at IS NULL
               AND COALESCE(web_product.is_active, inv_product.is_active) = 1
               AND (web_product_category.category_id = :category_id OR inv_product_category.category_id = :category_id)
            ORDER BY COALESCE(web_product.sort_order, inv_product.sort_order) ASC,
                     COALESCE(web_product.created_at, inv_product.created_at) DESC
            LIMIT :limit OFFSET :offset
         ')
         ->execute([
            'category_id' => $category_id,
            'language_id' => $lang_id,
            'limit' => $limit,
            'offset' => $offset,
         ])
         ->fetchAll();
   }

   public function search(string $query, int $lang_id, int $limit = 10, int $offset = 0): array {
      return $this->database
         ->prepare('SELECT
               inv_product.id,
               COALESCE(web_product.code, inv_product.code) AS code,
               COALESCE(web_product.is_active, inv_product.is_active) AS is_active,
               COALESCE(web_product.sort_order, inv_product.sort_order) AS sort_order,
               COALESCE(web_product.created_at, inv_product.created_at) AS created_at,
               COALESCE(web_product.updated_at, inv_product.updated_at) AS updated_at,
               COALESCE(web_product.deleted_at, inv_product.deleted_at) AS deleted_at,
               COALESCE(web_product_translate.title, inv_product_translate.title) AS title,
               COALESCE(web_product_translate.content, inv_product_translate.content) AS content,
               web_product_translate.url
            FROM inv_product

            LEFT JOIN web_product ON web_product.id = inv_product.id
               AND web_product.deleted_at IS NULL
            LEFT JOIN inv_product_translate ON inv_product_translate.product_id = inv_product.id
               AND inv_product_translate.language_id = :language_id
            LEFT JOIN web_product_translate ON web_product_translate.product_id = inv_product.id
               AND web_product_translate.language_id = :language_id
            WHERE inv_product.deleted_at IS NULL
               AND COALESCE(web_product.is_active, inv_product.is_active) = 1
               AND (
                  COALESCE(web_product.code, inv_product.code) LIKE :query
                  OR COALESCE(web_product_translate.title, inv_product_translate.title) LIKE :query
                  OR COALESCE(web_product_translate.content, inv_product_translate.content) LIKE :query
               )
            ORDER BY COALESCE(web_product.sort_order, inv_product.sort_order) ASC,
                     COALESCE(web_product.created_at, inv_product.created_at) DESC
            LIMIT :limit OFFSET :offset
         ')
         ->execute([
            'query' => "%{$query}%",
            'language_id' => $lang_id,
            'limit' => $limit,
            'offset' => $offset,
         ])
         ->fetchAll();
   }
}
