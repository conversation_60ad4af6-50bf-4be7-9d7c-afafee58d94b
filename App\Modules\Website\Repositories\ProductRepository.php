<?php

declare(strict_types=1);

namespace App\Modules\Website\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class ProductRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'product'
   ) {
      $this->database->prefix('web_');
   }

   public function findAll(int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               web_product.*,
               web_product_translate.title,
               web_product_translate.content,
               web_product_translate.url,
               web_product_translate.meta_title,
               web_product_translate.meta_description,
               web_product_translate.meta_keywords
            FROM web_product

            LEFT JOIN web_product_translate ON web_product_translate.product_id = web_product.id
               AND web_product_translate.language_id = :language_id
            WHERE web_product.deleted_at IS NULL
               AND web_product.is_active = 1
            ORDER BY web_product.sort_order ASC, web_product.created_at DESC
         ')
         ->execute([
            'language_id' => $lang_id
         ])
         ->fetchAll();
   }

   public function findOne(int $id, int $lang_id): array|false {
      return $this->database
         ->prepare('SELECT
               web_product.*,
               COALESCE(web_product_translate.title, default_translate.title) AS `title`,
               COALESCE(web_product_translate.content, default_translate.content) AS `content`,
               COALESCE(web_product_translate.url, default_translate.url) AS `url`,
               COALESCE(web_product_translate.meta_title, default_translate.meta_title) AS `meta_title`,
               COALESCE(web_product_translate.meta_description, default_translate.meta_description) AS `meta_description`,
               COALESCE(web_product_translate.meta_keywords, default_translate.meta_keywords) AS `meta_keywords`
            FROM web_product

            LEFT JOIN web_product_translate ON web_product_translate.product_id = web_product.id
               AND web_product_translate.language_id = :language_id
            LEFT JOIN web_product_translate AS default_translate ON default_translate.product_id = web_product.id
               AND default_translate.language_id = 1
            WHERE web_product.deleted_at IS NULL
               AND web_product.id = :id
         ')
         ->execute([
            'id' => $id,
            'language_id' => $lang_id,
         ])
         ->fetch();
   }

   public function findByUrl(string $url, int $lang_id): array|false {
      return $this->database
         ->prepare('SELECT
               web_product.*,
               web_product_translate.title,
               web_product_translate.content,
               web_product_translate.url,
               web_product_translate.meta_title,
               web_product_translate.meta_description,
               web_product_translate.meta_keywords
            FROM web_product

            JOIN web_product_translate ON web_product_translate.product_id = web_product.id
               AND web_product_translate.language_id = :language_id
            WHERE web_product.deleted_at IS NULL
               AND web_product.is_active = 1
               AND web_product_translate.url = :url
         ')
         ->execute([
            'url' => $url,
            'language_id' => $lang_id,
         ])
         ->fetch();
   }

   public function findCategory(int $product_id, int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               web_category.id,
               web_category_translate.title,
               web_category_translate.url
            FROM web_product_category

            JOIN web_category ON web_category.id = web_product_category.category_id
               AND web_category.deleted_at IS NULL
               AND web_category.is_active = 1
            LEFT JOIN web_category_translate ON web_category_translate.category_id = web_product_category.category_id
               AND web_category_translate.language_id = :language_id
            WHERE web_product_category.product_id = :product_id
            ORDER BY web_category.sort_order ASC
         ')
         ->execute([
            'product_id' => $product_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();
   }

   public function findManufacturer(int $product_id, int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               web_manufacturer.id,
               web_manufacturer_translate.title,
               web_manufacturer.image_path
            FROM web_product_manufacturer

            JOIN web_manufacturer ON web_manufacturer.id = web_product_manufacturer.manufacturer_id
               AND web_manufacturer.deleted_at IS NULL
               AND web_manufacturer.is_active = 1
            LEFT JOIN web_manufacturer_translate ON web_manufacturer_translate.manufacturer_id = web_product_manufacturer.manufacturer_id
               AND web_manufacturer_translate.language_id = :language_id
            WHERE web_product_manufacturer.product_id = :product_id
            ORDER BY web_manufacturer.sort_order ASC
         ')
         ->execute([
            'product_id' => $product_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();
   }

   public function findAttr(int $product_id, int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               web_attr.id,
               web_attr_translate.title
            FROM web_product_attr

            JOIN web_attr ON web_attr.id = web_product_attr.attr_id
               AND web_attr.deleted_at IS NULL
            LEFT JOIN web_attr_translate ON web_attr_translate.attr_id = web_product_attr.attr_id
               AND web_attr_translate.language_id = :language_id
            WHERE web_product_attr.product_id = :product_id
            ORDER BY web_attr.sort_order ASC
         ')
         ->execute([
            'product_id' => $product_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();
   }

   public function findStandard(int $product_id, int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               web_standard.id,
               web_standard_translate.title,
               web_product_standard.value,
               web_standard.image_path
            FROM web_product_standard

            JOIN web_standard ON web_standard.id = web_product_standard.standard_id
               AND web_standard.deleted_at IS NULL
            LEFT JOIN web_standard_translate ON web_standard_translate.standard_id = web_product_standard.standard_id
               AND web_standard_translate.language_id = :language_id
            WHERE web_product_standard.product_id = :product_id
            ORDER BY web_standard.sort_order ASC
         ')
         ->execute([
            'product_id' => $product_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();
   }

   public function findImage(int $product_id): array {
      return $this->database
         ->prepare('SELECT
               web_product_image.id,
               web_product_image.product_id,
               web_product_image.image_path
            FROM web_product_image
            WHERE web_product_image.deleted_at IS NULL
               AND web_product_image.image_path IS NOT NULL
               AND web_product_image.product_id = :product_id
            ORDER BY web_product_image.sort_order ASC, web_product_image.created_at ASC
         ')
         ->execute([
            'product_id' => $product_id,
         ])
         ->fetchAll();
   }

   public function findByCategoryId(int $category_id, int $lang_id, int $limit = 10, int $offset = 0): array {
      return $this->database
         ->prepare('SELECT
               web_product.*,
               web_product_translate.title,
               web_product_translate.content,
               web_product_translate.url
            FROM web_product

            JOIN web_product_category ON web_product_category.product_id = web_product.id
            LEFT JOIN web_product_translate ON web_product_translate.product_id = web_product.id
               AND web_product_translate.language_id = :language_id
            WHERE web_product.deleted_at IS NULL
               AND web_product.is_active = 1
               AND web_product_category.category_id = :category_id
            ORDER BY web_product.sort_order ASC, web_product.created_at DESC
            LIMIT :limit OFFSET :offset
         ')
         ->execute([
            'category_id' => $category_id,
            'language_id' => $lang_id,
            'limit' => $limit,
            'offset' => $offset,
         ])
         ->fetchAll();
   }

   public function search(string $query, int $lang_id, int $limit = 10, int $offset = 0): array {
      return $this->database
         ->prepare('SELECT
               web_product.*,
               web_product_translate.title,
               web_product_translate.content,
               web_product_translate.url
            FROM web_product

            LEFT JOIN web_product_translate ON web_product_translate.product_id = web_product.id
               AND web_product_translate.language_id = :language_id
            WHERE web_product.deleted_at IS NULL
               AND web_product.is_active = 1
               AND (
                  web_product.code LIKE :query
                  OR web_product_translate.title LIKE :query
                  OR web_product_translate.content LIKE :query
               )
            ORDER BY web_product.sort_order ASC, web_product.created_at DESC
            LIMIT :limit OFFSET :offset
         ')
         ->execute([
            'query' => "%{$query}%",
            'language_id' => $lang_id,
            'limit' => $limit,
            'offset' => $offset,
         ])
         ->fetchAll();
   }
}
