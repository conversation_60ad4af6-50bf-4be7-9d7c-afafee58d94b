// vuetify
import { AllowedComponentProps, VNodeProps } from "vue";
import { VBtn, VCard, VContainer, VDataTable, VDatePicker, VList, VListItem, VSelect, VTextField, VToolbar } from "vuetify/components";

/* @vue-ignore */
type ComponentProps<C extends Component> = C extends new (...args: any) => any ? Omit<InstanceType<C>["$props"], keyof VNodeProps | keyof AllowedComponentProps> : never;
/* @ts-ignore */
type UnwrapReadonlyArray<A> = A extends Readonly<Array<infer I>> ? I : A;

type BaseTList = Partial<Omit<ComponentProps<typeof VList>, "itemProps">> & {
  itemType?: "subheader" | "divider";
};
type TListWithChildren = BaseTList & {
  itemProps: Partial<ComponentProps<typeof VListItem>> & {
    value: string;
  };
  children: TList[];
};
type TListWithoutChildren = BaseTList & {
  itemProps?: Partial<ComponentProps<typeof VListItem>>;
  children?: undefined;
};
/* @ts-ignore */
export type TList = TListWithChildren | TListWithoutChildren;
/* @ts-ignore */
export type TDataTable = Partial<ComponentProps<typeof VDataTable>>;
/* @ts-ignore */
export type TMultiSelect = Partial<ComponentProps<typeof VSelect>>;
/* @ts-ignore */
export type TDateField = Partial<ComponentProps<typeof VDatePicker>> & Partial<ComponentProps<typeof VTextField>>;
/* @ts-ignore */
export type TToolbar = Partial<ComponentProps<typeof VToolbar>>;
/* @ts-ignore */
export type TCard = Partial<ComponentProps<typeof VCard>>;
/* @ts-ignore */
export type TContainer = Partial<ComponentProps<typeof VContainer>>;
/* @ts-ignore */
export type TBtn = Partial<ComponentProps<typeof VBtn>>;

// enums
export enum EButton {
   RecordVariant = "text",
   RecordDensity = "comfortable"
}

export enum EToast {
   Success = "success",
   Error = "error",
   Danger = "danger",
   Warning = "warning"
}

export enum EUser {
   "user" = 300,
   "admin" = 400
}

export enum ELanguage {
   "tr-TR" = 1,
   "en-US" = 2
}

// interfaces
export interface IRoute extends RouteLocationNormalizedLoaded {
   params: {
      id: string;
   };
}

export interface IQuery<T> {
   onSuccess?: (data: T) => void;
   onError?: (error: any) => void;
   enabled?: Ref<boolean>;
   language?: Ref<number>;
   params?: Record<string, any>;
}

export interface IMutation {
   invalidate?: string[];
}

export interface IResponse<T> {
   success: boolean;
   message: string;
   data: T;
   error: any;
   meta: any;
   status: number;
}

export interface ITranslate {
   language_id: number;
   title?: string;
   content?: string;
}

export interface IStatus {
   isError: boolean | number;
   isPending: boolean;
   isSuccess: boolean;
}

export interface IHeader<T> {
   title?: string;
   key?: keyof T | "actions";
   width?: string;
   value?: string;
   sortable?: boolean;
   prepend?: boolean;
   append?: boolean;
   align?: "start" | "center" | "end";
   filterable?: boolean;
   selectable?: boolean;
   orderable?: boolean;
   format?: true | Intl.DateTimeFormatOptions;
}

export interface IToast {
   id?: number;
   title?: string;
   message?: string | null;
   date?: string | null;
   type?: string;
   timeout?: number;
   show?: boolean;
}

export interface ISnackbar {
   text: string;
   color?: string;
   timeout?: number;
}

// typeof initial values
export const TDialog = {
   show: false,
   loading: false,
   request: false,
   focus: false,
   validate: false,
   title: "",
   titleColor: "",
   acceptText: "",
   acceptColor: "",
   acceptVariant: undefined,
   cancelText: "",
   cancelColor: "error",
   cancelVariant: undefined,
   label: "",
   message: "",
   rules: [] as any[],
   width: 320,
   callback: Function as (item: any) => any,
   resolve: (_value: boolean) => {},
   reject: (_value: boolean) => {}
};
