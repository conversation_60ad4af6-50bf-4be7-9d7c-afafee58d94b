export const registerI18n = async (): Promise<void> => {
   const appStore = useAppStore();
   appStore.setI18nLoading(true);
   await loadLocales(getUserLocale())
      .then(() => {
         setUserLocale(getUserLocale());
      })
      .finally(() => {
         appStore.setI18nLoading(false);
      });
};

export const i18n = createI18n({
   locale: localStorage.getItem(appConfig.key.locale) as string,
   legacy: false,
   fallbackLocale: appConfig.default.locale,
   globalInjection: false,
   missingWarn: false,
   fallbackWarn: false
});
