<?php

declare(strict_types=1);

use System\Migration\Migration;

class web_category extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `web_category` (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `category_id` INT NOT NULL DEFAULT 0,
         `code` VARCHAR(50) NOT NULL,
         `image_path` VARCHAR(250) NULL DEFAULT NULL,
         `is_active` BOOLEAN NOT NULL DEFAULT 1,
         `sort_order` INT NOT NULL DEFAULT 0,
         `parent_id` INT NOT NULL DEFAULT 0,
         `group_id` INT NOT NULL DEFAULT 0,
         {$this->defaults()}
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `web_category`");
   }
}
