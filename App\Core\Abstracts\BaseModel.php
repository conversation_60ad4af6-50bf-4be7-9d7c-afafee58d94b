<?php

declare(strict_types=1);

namespace App\Core\Abstracts;

abstract class BaseModel {
   public string $created_at;
   public int $created_by;
   public ?string $deleted_at;
   public ?int $deleted_by;
   public string $updated_at;
   public ?int $updated_by;

   public function fromRequest(array $data): self {
      foreach ($data as $key => $value) {
         if (property_exists($this, $key)) {
            $this->$key = $value;
         }
      }
      return $this;
   }
}
