import { IAttr, IAttrStore } from "../utils/types";

export const useGetAttrAll = (payload?: IQuery<IAttr[]>) => {
   const options = computed(() => ({
      queryKey: ["attr", "attrAll"],
      queryFn: async () => {
         return (await appAxios.get("/inventory/attr/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetAttrById = (payload?: { id?: MaybeRef<string> } & IQuery<IAttr>) => {
   const options = computed(() => ({
      queryKey: ["attr", "attrById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/inventory/attr/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateAttr = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["attr", "updateAttr"],
      mutationFn: async (data: IAttrStore): Promise<IResponse<IAttr>> => {
         return (await appAxios.put("/inventory/attr/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["attr"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useCreateAttr = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["attr", "createAttr"],
      mutationFn: async (data: IAttrStore): Promise<IResponse<IAttr>> => {
         return (await appAxios.post("/inventory/attr/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["attr"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};
