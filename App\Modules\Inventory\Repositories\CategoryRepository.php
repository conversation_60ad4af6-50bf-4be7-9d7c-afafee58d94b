<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class CategoryRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'category'
   ) {
      $this->database->prefix('inv_');
   }

   public function findAll(int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_category.*,
               inv_category_translate.title,
               inv_category_translate.content
            FROM inv_category

            LEFT JOIN inv_category_translate ON inv_category_translate.category_id = inv_category.id
               AND inv_category_translate.language_id = :language_id
            WHERE inv_category.deleted_at IS NULL
         ')
         ->execute([
            'language_id' => $lang_id
         ])
         ->fetchAll();
   }

   public function findOne(int $id, int $lang_id): array|false {
      return $this->database
         ->prepare('SELECT
               inv_category.*,
               COALESCE(inv_category_translate.title, default_translate.title) AS `title`,
               COALESCE(inv_category_translate.content, default_translate.content) AS `content`
            FROM inv_category

            LEFT JOIN inv_category_translate ON inv_category_translate.category_id = inv_category.id
               AND inv_category_translate.language_id = :language_id
            LEFT JOIN inv_category_translate AS default_translate ON default_translate.category_id = inv_category.id
               AND default_translate.language_id = 1
            WHERE inv_category.deleted_at IS NULL
               AND inv_category.id = :id
         ')
         ->execute([
            'id' => $id,
            'language_id' => $lang_id,
         ])
         ->fetch();
   }
}
