<template>
   <v-container
      v-bind="{ ...$attrs }"
      class="overflow-visible">
      <v-skeleton-loader v-if="props.loading" />

      <div v-else-if="props.error">Aradığınız sayfa bulunamadı</div>
      <template v-else>
         <v-form
            v-if="props.form"
            ref="form"
            @submit.prevent="form.validate && props.form">
            <slot />
         </v-form>
         <slot v-else />
      </template>
   </v-container>
</template>

<script lang="ts" setup>
import type { TContainer } from "@/utils/types";

const form = ref();

type TProps = {
   loading?: boolean;
   error?: boolean;
   form?: (e: Event) => void;
};

const props = withDefaults(defineProps<TContainer & TProps>(), {
   loading: false,
   error: false
});
</script>
