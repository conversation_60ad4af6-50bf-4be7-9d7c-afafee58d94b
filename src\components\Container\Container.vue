<template>
   <v-container
      v-bind="{ ...$attrs }"
      class="overflow-visible">
      <v-skeleton-loader v-if="props.loading" />

      <div v-else-if="props.error">Aradığınız sayfa bulunamadı</div>
      <slot v-else />
   </v-container>
</template>

<script lang="ts" setup>
import type { TContainer } from "@/utils/types";

type TProps = {
   loading?: boolean;
   error?: boolean;
};

const props = withDefaults(defineProps<TContainer & TProps>(), {
   loading: false,
   error: false
});
</script>
