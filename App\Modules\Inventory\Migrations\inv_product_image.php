<?php

declare(strict_types=1);

use System\Migration\Migration;

class inv_product_image extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `inv_product_image` (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `product_id` INT NOT NULL DEFAULT 0,
         `image_path` VARCHAR(250) NULL DEFAULT NULL,
         `sort_order` INT NOT NULL DEFAULT 0,
         {$this->defaults()}
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `inv_product_image`");
   }
}
