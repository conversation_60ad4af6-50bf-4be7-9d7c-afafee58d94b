<?php

declare(strict_types=1);

namespace App\Modules\Website\Controllers;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Website\DTOs\CategoryDTO;
use App\Modules\Website\Models\CategoryModel;
use App\Modules\Website\Services\CategoryService;

/**
 * @OA\Tag(name="Website Category", description="Website kategori işlemleri")
 */
class CategoryController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected CategoryService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Website Category"}, path="/website/category/", summary="Website kategori listesi",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getAllCategory() {
      $this->response(function () {
         $result = $this->service->getAll($this->language(), CategoryModel::class);
         return $result;
      });
   }

   /**
    * @OA\Get(
    *    tags={"Website Category"}, path="/website/category/{id}", summary="Website kategori detayı",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getCategory(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getOne($id, $this->language(), CategoryModel::class);
         return $result;
      });
   }

   /**
    * @OA\Post(
    *    tags={"Website Category"}, path="/website/category/", summary="Website kategori override oluştur",
    *    @OA\Response(response=201, description="Created"),
    *    @OA\RequestBody(@OA\JsonContent(
    *       @OA\Property(property="id", type="integer", example=1, description="Inventory category ID"),
    *       @OA\Property(property="code", type="string", example="WEB-CAT001"),
    *       @OA\Property(property="image_path", type="string", example="/uploads/category.jpg"),
    *       @OA\Property(property="is_active", type="integer", example=1),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="parent_id", type="integer", example=0),
    *       @OA\Property(property="group_id", type="integer", example=1),
    *       @OA\Property(property="translate", type="object",
    *          @OA\Property(property="language_id", type="integer", example=1),
    *          @OA\Property(property="title", type="string", example="Kategori Başlığı"),
    *          @OA\Property(property="content", type="string", example="Kategori açıklaması"),
    *          @OA\Property(property="url", type="string", example="kategori-basligi"),
    *          @OA\Property(property="meta_title", type="string", example="Meta Başlık"),
    *          @OA\Property(property="meta_description", type="string", example="Meta açıklama"),
    *          @OA\Property(property="meta_keywords", type="string", example="anahtar, kelimeler")
    *       )
    *    ))
    * )
    */
   public function createCategory() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new CategoryDTO();
         $dto->fromRequest($request);
         $result = $this->service->createCategory($dto, $this->language());
         return $result;
      }, code: 201);
   }

   /**
    * @OA\Put(
    *    tags={"Website Category"}, path="/website/category/", summary="Website kategori override güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(@OA\JsonContent(
    *       @OA\Property(property="id", type="integer", example=1, description="Inventory category ID"),
    *       @OA\Property(property="code", type="string", example="WEB-CAT001"),
    *       @OA\Property(property="image_path", type="string", example="/uploads/category.jpg"),
    *       @OA\Property(property="is_active", type="integer", example=1),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="parent_id", type="integer", example=0),
    *       @OA\Property(property="group_id", type="integer", example=1),
    *       @OA\Property(property="translate", type="object",
    *          @OA\Property(property="language_id", type="integer", example=1),
    *          @OA\Property(property="title", type="string", example="Kategori Başlığı"),
    *          @OA\Property(property="content", type="string", example="Kategori açıklaması"),
    *          @OA\Property(property="url", type="string", example="kategori-basligi"),
    *          @OA\Property(property="meta_title", type="string", example="Meta Başlık"),
    *          @OA\Property(property="meta_description", type="string", example="Meta açıklama"),
    *          @OA\Property(property="meta_keywords", type="string", example="anahtar, kelimeler")
    *       )
    *    ))
    * )
    */
   public function overrideCategory() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new CategoryDTO();
         $dto->fromRequest($request);
         $result = $this->service->overrideCategory($dto, $this->language());
         return $result;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Website Category"}, path="/website/category/{id}", summary="Website kategori override sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer", description="Inventory category ID"))
    * )
    */
   public function deleteCategory(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->deleteCategory($id);
         return $result;
      });
   }
}
