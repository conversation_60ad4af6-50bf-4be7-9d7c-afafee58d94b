<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class ProductRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'product'
   ) {
      $this->database->prefix('inv_');
   }

   public function findAll(int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_product.*,
               inv_product_translate.title,
               inv_product_translate.content
            FROM inv_product

            LEFT JOIN inv_product_translate ON inv_product_translate.product_id = inv_product.id
               AND inv_product_translate.language_id = :language_id
            WHERE inv_product.deleted_at IS NULL
         ')
         ->execute([
            'language_id' => $lang_id
         ])
         ->fetchAll();
   }

   public function findOne(int $id, int $lang_id): array|false {
      return $this->database
         ->prepare('SELECT
               inv_product.*,
               COALESCE(inv_product_translate.title, default_translate.title) AS `title`,
               COALESCE(inv_product_translate.content, default_translate.content) AS `content`
            FROM inv_product

            LEFT JOIN inv_product_translate ON inv_product_translate.product_id = inv_product.id
               AND inv_product_translate.language_id = :language_id
            LEFT JOIN inv_product_translate AS default_translate ON default_translate.product_id = inv_product.id
               AND default_translate.language_id = 1
            WHERE inv_product.deleted_at IS NULL
               AND inv_product.id = :id
         ')
         ->execute([
            'id' => $id,
            'language_id' => $lang_id,
         ])
         ->fetch();
   }

   public function findCategory(int $product_id, int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_category.id,
               inv_category_translate.title
            FROM inv_product_category

            JOIN inv_category ON inv_category.id = inv_product_category.category_id
               AND inv_category.deleted_at IS NULL
            LEFT JOIN inv_category_translate ON inv_category_translate.category_id = inv_product_category.category_id
               AND inv_category_translate.language_id = :language_id
            WHERE inv_product_category.product_id = :product_id
         ')
         ->execute([
            'product_id' => $product_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();
   }

   public function findManufacturer(int $product_id, int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_manufacturer.id,
               inv_manufacturer_translate.title
            FROM inv_product_manufacturer

            JOIN inv_manufacturer ON inv_manufacturer.id = inv_product_manufacturer.manufacturer_id
               AND inv_manufacturer.deleted_at IS NULL
            LEFT JOIN inv_manufacturer_translate ON inv_manufacturer_translate.manufacturer_id = inv_product_manufacturer.manufacturer_id
               AND inv_manufacturer_translate.language_id = :language_id
            WHERE inv_product_manufacturer.product_id = :product_id
         ')
         ->execute([
            'product_id' => $product_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();
   }

   public function findAttr(int $product_id, int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_attr.id,
               inv_attr_translate.title
            FROM inv_product_attr

            JOIN inv_attr ON inv_attr.id = inv_product_attr.attr_id
               AND inv_attr.deleted_at IS NULL
            LEFT JOIN inv_attr_translate ON inv_attr_translate.attr_id = inv_product_attr.attr_id
               AND inv_attr_translate.language_id = :language_id
            WHERE inv_product_attr.product_id = :product_id
         ')
         ->execute([
            'product_id' => $product_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();
   }

   public function findStandard(int $product_id, int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_standard.id,
               inv_standard_translate.title,
               inv_product_standard.value,
               inv_standard.image_path
            FROM inv_product_standard

            JOIN inv_standard ON inv_standard.id = inv_product_standard.standard_id
               AND inv_standard.deleted_at IS NULL
            LEFT JOIN inv_standard_translate ON inv_standard_translate.standard_id = inv_product_standard.standard_id
               AND inv_standard_translate.language_id = :language_id
            WHERE inv_product_standard.product_id = :product_id
         ')
         ->execute([
            'product_id' => $product_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();
   }

   public function findImage(int $product_id): array {
      return $this->database
         ->prepare('SELECT
               inv_product_image.id,
               inv_product_image.product_id,
               inv_product_image.image_path
            FROM inv_product_image
            WHERE inv_product_image.deleted_at IS NULL
               AND inv_product_image.image_path IS NOT NULL
               AND inv_product_image.product_id = :product_id
            ORDER BY inv_product_image.sort_order ASC, inv_product_image.created_at ASC
         ')
         ->execute([
            'product_id' => $product_id,
         ])
         ->fetchAll();
   }
}
