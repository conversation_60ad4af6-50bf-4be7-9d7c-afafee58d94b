<?php

declare(strict_types=1);

namespace App\Modules\Inventory\DTOs;

use App\Core\Abstracts\BaseDTO;

class ProductDTO extends BaseDTO {
   public function __construct(
      public ?int $id = null,
      public ?string $code = null,
      public ?array $image_path = null,
      public int $is_active = 1,
      public int $sort_order = 1,
      public ?array $translate = null,

      public ?array $product_category = null,
      public ?array $product_manufacturer = null,
      public ?array $product_attr = null,
      public ?array $product_standard = null,
   ) {
   }
}
