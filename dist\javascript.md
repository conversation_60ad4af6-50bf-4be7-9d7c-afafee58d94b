# Javascript
## Typescript Interface ve Type Farkı
Primitive tiplerin (string, number, undefined, null, boolean) yanı sıra nesnenin de data tipini belirlememiz gerekiyor. Tam da burada Interface ve Type kullanılıyor.  
Interface nesne tipini belirlerken kullanılıyor. Type da Interface’in yaptı<PERSON><PERSON> neredeyse her şeyi yapabildiği gibi primitive tipler (string, number, undefined, null, boolean) veya union tipler için de kullanılabiliyor.  
Aralarındaki en temel fark; Interface genişletilebilirken Type genişletilemez. 

```javascript
interface DropdownSelect {
  id: string;
  value: number;
  name: string;
  status: boolean;
  onItemSelect(): void;
}

type info = 'nature';

type Props = {
  id: string;
  value: number;
  name: string;
  status: boolean;
  onItemSelect(): void;
}

type SortValue = 'asc' | 'desc'; //union

type plusOneFn = (value: number) => number;
```
Gördüğünüz gibi Type ile nesne tipini belirlememizin dışında union, string, fonksiyon gibi tipleri tanımlarken de kullanabiliyoruz. Ancak Interface ile yalnızca nesne tipini belirleyebiliriz.

**Genişletilebilirlik**
```javascript
interface Car {
  mileage: number;
  year: number;
  color: string;
}

interface Car {
  transmission: string;
}
```
Interface’de genişletme yapmak oldukça kolay. Ancak buradaki genişletme işleminin aynısını Type’da yapmamız mümkün değil. Eğer aynı yazım şeklini kullanmaya kalkarsak Duplicate identifier hatası alırız. Fakat başka türlü çözümler üretmek mümkün.

```javascript
type CarCondition = {
  mileage: number;
  year: number;
}

type CarTransmission = {
  transmission: string
}

interface CarColor {
  color: string;
}

type Car = CarCondition & CarTransmission & CarColor;
```
`Intersection` birden fazla data tipini başka bir data tipinde birleştirmemize olanak sağlar. Bu örnekle aslında type Car'da birden fazla type ve interface'i birleştirdik, dolayısıyla genişletmiş olduk. Ancak tabii çok daha farklı bir yol kullanarak. Bunun yanında Union tip ile birleştirmemiz de mümkün.

```javascript
type Car = CarCondition | CarTransmission  | CarColor;
```
`Intersection` birden fazla tipi tek bir tipte birleştirirken Union ise veri tipinin, tanımlanan türlerden biri olabileceğini belirtir. Herhangi biri olabileceği gibi hepsi de olabilir. Bunu matematikteki mantık konusundan hatırlayabileceğiniz gibi JavaScript'teki mantıksal operatörlerden de hatırlayabilirsiniz. Intersection( & ) tipini "ve", Union( | ) tipini ise "veya" gibi düşünebiliriz.
