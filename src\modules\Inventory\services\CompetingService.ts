import { ICompeting, ICompetingStore } from "../utils/types";

export const useGetCompetingAll = (payload?: IQuery<ICompeting[]>) => {
   const options = computed(() => ({
      queryKey: ["competing", "competingAll"],
      queryFn: async () => {
         return (await appAxios.get("/inventory/competing/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetCompetingById = (payload?: { id?: MaybeRef<string> } & IQuery<ICompeting>) => {
   const options = computed(() => ({
      queryKey: ["competing", "competingById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/inventory/competing/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateCompeting = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["competing", "updateCompeting"],
      mutationFn: async (data: ICompetingStore): Promise<IResponse<ICompeting>> => {
         return (await appAxios.put("/inventory/competing/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["competing"] });
      }
   });
};

export const useCreateCompeting = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["competing", "createCompeting"],
      mutationFn: async (data: ICompetingStore): Promise<IResponse<ICompeting>> => {
         return (await appAxios.post("/inventory/competing/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["competing"] });
      }
   });
};
