# Veribis CRM
## CRM Kullanım Rehberi
<iframe src="files/crm-kullanım-rehberi.pdf" height="640px"></iframe>

## Kur Güncelleme
Veribis kur değerlerini çekemezse, teklife eklenmek istenen ürünün KUR değerini değiştirmek için aşağı açılır listede EUR, USD vb. gibi KUR değerleri listelenmez

**Çözüm**  
Aşağıdaki sorgu çalıştırılarak Currency tablosu kontrol edilir.
```sql
USE [VeribisCRM]
SELECT * FROM Currency ORDER BY Id DESC
```

Teklifin eklenmek istendiği tarihe ait EUR, USD vb. gibi KUR bilgileri yer almıyor ise önce ilgili tarihe ait TRY girdisi (örnek ID 17424) ile silinir.
```sql
USE [VeribisCRM]
DELETE FROM Currency WHERE Id=17424
```

Aşağıdaki sorgu ile (örnek tarih 13.06.2023) yeni KUR bilgileri getirilir.
```sql
USE [VeribisCRM]
GO
DECLARE @return_value INT
EXEC @return_value = [dbo].[Proc_Get_Currency]
@curDate = '2023-06-13'
SELECT 'Return Value' = @return_value
GO
```

## Senkronizasyon Yenileme
Mikro'da yeni açılan Cari Hesap, Stok Kartı vb. girdiler 10-15 saniye içinde VeribisCRM'de görünmesi gerekir. Eğer görünmüyorsa senkronizasyonda problem olabilir.

- Servislerden `VeribisCrmMikroService` servisini durdur
- C:\Veribis\MikroCrmService\_config yolunda bulunan `time_appliction.txt` dosyasındaki tarihi geri bir tarihe çek (örnek olarak 1 yıl öncesi)
- C:\Veribis\MikroCrmService\_logs yolunda bulunan logları temizle
- Servislerden `VeribisCrmMikroService` servisini başlat
- Logları kontrol et
