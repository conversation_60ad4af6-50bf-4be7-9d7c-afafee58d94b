<?php

declare(strict_types=1);

use System\Migration\Migration;

class inv_manufacturer_translate extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `inv_manufacturer_translate` (
         `manufacturer_id` INT NOT NULL DEFAULT 0,
         `language_id` INT NOT NULL DEFAULT 0,
         `title` VARCHAR(250) NOT NULL,
         `content` TEXT NULL DEFAULT NULL,
         {$this->defaults()},
         PRIMARY KEY (`manufacturer_id`, `language_id`)
      )");
      // FOREIGN KEY (`manufacturer_id`) REFERENCES `manufacturer`(`id`),
      // FOREIGN KEY (`language_id`) REFERENCES `language`(`id`)

      $this->database->table('inv_manufacturer_translate')->insert([
         'title' => 'ABC Güvenlik',
         'content' => 'ABC Güvenlik, iş güvenliği ekipmanları üreticisi.',
         'language_id' => 1,
         'manufacturer_id' => 1,
      ])->prepare()->execute();

      $this->database->table('inv_manufacturer_translate')->insert([
         'title' => 'ABC Safety',
         'content' => 'ABC Safety, manufacturer of work safety equipment.',
         'language_id' => 2,
         'manufacturer_id' => 1,
      ])->prepare()->execute();

      $this->database->table('inv_manufacturer_translate')->insert([
         'title' => 'XYZ Güvenlik',
         'content' => 'XYZ Güvenlik, iş güvenliği ekipmanları üreticisi.',
         'language_id' => 1,
         'manufacturer_id' => 2,
      ])->prepare()->execute();

      $this->database->table('inv_manufacturer_translate')->insert([
         'title' => 'XYZ Safety',
         'content' => 'XYZ Safety, manufacturer of work safety equipment.',
         'language_id' => 2,
         'manufacturer_id' => 2,
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `inv_manufacturer_translate`");
   }
}
