<?php

declare(strict_types=1);

use App\Core\Middlewares\Auth;
use App\Core\Middlewares\Mikro;
use App\Modules\Inventory\Controllers\AttrController;
use App\Modules\Inventory\Controllers\ImageController;
use App\Modules\Inventory\Controllers\ProductController;
use App\Modules\Inventory\Controllers\CategoryController;
use App\Modules\Inventory\Controllers\StandardController;
use App\Modules\Inventory\Controllers\CompetingController;
use App\Modules\Inventory\Controllers\ManufacturerController;

/** @var System\Router\Router $router */

// Image routes for image_path
$router->prefix('v1/image')->middleware([Auth::class])->group(function () use ($router) {
   $router->post('/create', [ImageController::class, 'uploadImage']);
   $router->post('/delete', [ImageController::class, 'deleteImage']);
});

// Product routes
$router->prefix('v1/inventory/product')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [ProductController::class, 'getAllProduct']);
   $router->post('/', [ProductController::class, 'createProduct']);
   $router->put('/', [ProductController::class, 'updateProduct']);
   $router->delete('/{id}', [ProductController::class, 'deleteProduct'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [ProductController::class, 'getProduct'])->where(['id' => '([0-9]+)']);
});
$router->prefix('v1/product/mikro')->middleware([Mikro::class])->group(function () use ($router) {
   $router->post('/', function() {
      echo "selam";
   });
});

// Competing routes
$router->prefix('v1/inventory/competing')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [CompetingController::class, 'getAllCompeting']);
   $router->post('/', [CompetingController::class, 'createCompeting']);
   $router->put('/', [CompetingController::class, 'updateCompeting']);
   $router->delete('/{id}', [CompetingController::class, 'deleteCompeting'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [CompetingController::class, 'getCompeting'])->where(['id' => '([0-9]+)']);
});

// Category routes
$router->prefix('v1/inventory/category')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [CategoryController::class, 'getAllCategory']);
   $router->post('/', [CategoryController::class, 'createCategory']);
   $router->put('/', [CategoryController::class, 'updateCategory']);
   $router->delete('/{id}', [CategoryController::class, 'deleteCategory'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [CategoryController::class, 'getCategoryById'])->where(['id' => '([0-9]+)']);
});

// Manufacturer routes
$router->prefix('v1/inventory/manufacturer')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [ManufacturerController::class, 'getAllManufacturer']);
   $router->post('/', [ManufacturerController::class, 'createManufacturer']);
   $router->put('/', [ManufacturerController::class, 'updateManufacturer']);
   $router->delete('/{id}', [ManufacturerController::class, 'deleteManufacturer'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [ManufacturerController::class, 'getManufacturer'])->where(['id' => '([0-9]+)']);
});

// Attribute routes
$router->prefix('v1/inventory/attr')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [AttrController::class, 'getAllAttr']);
   $router->post('/', [AttrController::class, 'createAttr']);
   $router->put('/', [AttrController::class, 'updateAttr']);
   $router->delete('/{id}', [AttrController::class, 'deleteAttr'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [AttrController::class, 'getAttr'])->where(['id' => '([0-9]+)']);
});

// Standard routes
$router->prefix('v1/inventory/standard')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [StandardController::class, 'getAllStandard']);
   $router->post('/', [StandardController::class, 'createStandard']);
   $router->put('/', [StandardController::class, 'updateStandard']);
   $router->delete('/{id}', [StandardController::class, 'deleteStandard'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [StandardController::class, 'getStandard'])->where(['id' => '([0-9]+)']);
});
