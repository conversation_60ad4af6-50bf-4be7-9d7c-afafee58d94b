<?php

declare(strict_types=1);

namespace App\Modules\Inventory\DTOs;

use App\Core\Abstracts\BaseDTO;

class CompetingDTO extends BaseDTO {
   public function __construct(
      public ?int $id = null,
      public ?string $title = null,
      public ?string $content = null,
      public ?float $price = null,
      public ?string $currency = null,
      public ?string $image_path = null,

      public ?array $competing_category = null,
      public ?array $competing_manufacturer = null,
      public ?array $competing_attr = null,
      public ?array $competing_standard = null,
      public ?array $competing_product = null,
   ) {
   }
}
