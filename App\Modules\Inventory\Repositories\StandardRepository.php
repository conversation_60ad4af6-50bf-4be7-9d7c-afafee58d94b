<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class StandardRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'standard'
   ) {
      $this->database->prefix('inv_');
   }

   public function findAll(int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_standard.*,
               inv_standard_translate.title,
               inv_standard_translate.content
            FROM inv_standard

            LEFT JOIN inv_standard_translate ON inv_standard_translate.standard_id = inv_standard.id
               AND inv_standard_translate.language_id = :language_id
            WHERE inv_standard.deleted_at IS NULL
         ')
         ->execute([
            'language_id' => $lang_id
         ])
         ->fetchAll();
   }

   public function findOne(int $id, int $lang_id): array|false {
      return $this->database
         ->prepare('SELECT
               inv_standard.*,
               COALESCE(inv_standard_translate.title, default_translate.title) AS `title`,
               COALESCE(inv_standard_translate.content, default_translate.content) AS `content`
            FROM inv_standard

            LEFT JOIN inv_standard_translate ON inv_standard_translate.standard_id = inv_standard.id
               AND inv_standard_translate.language_id = :language_id
            LEFT JOIN inv_standard_translate AS default_translate ON default_translate.standard_id = inv_standard.id
               AND default_translate.language_id = 1
            WHERE inv_standard.deleted_at IS NULL
               AND inv_standard.id = :id
         ')
         ->execute([
            'id' => $id,
            'language_id' => $lang_id,
         ])
         ->fetch();
   }
}
