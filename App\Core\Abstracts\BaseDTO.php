<?php

declare(strict_types=1);

namespace App\Core\Abstracts;

abstract class BaseDTO {
   public ?int $id = null;

   /**
    * Nesnenin özelliklerini dizi olarak döner.
    * Null veya boş string ('') olan özellikler filtrelenir, sonuç dizisine dahil edilmez.
    *
    * @return array filtrelenmiş özellikler dizisi
    */
   public function toArray(): array {
      return array_filter(get_object_vars($this), function ($value) {
         return !is_null($value) && $value !== '';
      });
   }

   /**
    * <PERSON><PERSON>len diziden, nesnenin özelliklerine karşılık gelen anahtarları seçer.
    *
    * @param array $data filtrelenecek veri dizisi
    * @return array nesnenin özelliklerine ait anahtar-değer çiftlerinden oluşan dizi
    */
   public function optionalArray(array $data): array {
      return array_intersect_key($data, array_flip(array_keys($this->toArray())));
   }

   /**
    * Verilen dizi içindeki <PERSON>, nesnenin mevcut özelliklerine atar.
    * Sadece nesnede var olan özellikler güncellenir, diğer anahtarlar yok sayılır.
    *
    * @param array $data özelliklerine değer atanacak veri dizisi
    */
   public function fromRequest(array $data): void {
      foreach ($data as $key => $value) {
         if (property_exists($this, $key)) {
            $this->$key = $value;
         }
      }
   }
}
