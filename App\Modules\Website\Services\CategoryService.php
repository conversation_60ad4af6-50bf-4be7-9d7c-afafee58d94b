<?php

declare(strict_types=1);

namespace App\Modules\Website\Services;

use System\Upload\Upload;
use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Website\DTOs\CategoryDTO;
use App\Modules\Website\Models\CategoryModel;
use App\Modules\Website\Repositories\CategoryRepository;

class CategoryService extends BaseService {
   /** @var CategoryRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Upload $upload,
      protected Validation $validation,
      CategoryRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function overrideCategory(CategoryDTO $dto, int $lang_id): CategoryModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'parent_id' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
         ]);

         $check = $this->repository->findBy(['category_id' => $dto->category_id]);

         if (empty($check)) {
            $result = $this->repository->create([
               'category_id' => $dto->category_id,
               'is_active' => $dto->is_active,
               'sort_order' => $dto->sort_order,
               'parent_id' => $dto->parent_id,
            ]);

            if ($result->affectedRows() <= 0) {
               throw new SystemException('Failed to create the web category record', 400);
            }
         } else {
            $result = $this->repository->update([
               'is_active' => $dto->is_active,
               'sort_order' => $dto->sort_order,
               'parent_id' => $dto->parent_id,
            ], [
               'category_id' => $dto->category_id
            ]);

            if ($result->affectedRows() <= 0) {
               throw new SystemException('Failed to update the web category record', 400);
            }
         }

         $this->translate($dto->translate, [
            'category_id' => $dto->category_id,
         ], 'category_translate');

         return $this->getOne($dto->category_id, $lang_id, CategoryModel::class);
      });
   }

   public function deleteCategory(int $id): bool {
      return $this->transaction(function () use ($id) {
         // Sadece web override'ı sil, inventory'yi dokunma
         $webCategory = $this->database
            ->table('web_category')
            ->select()
            ->where(['category_id'])
            ->prepare()
            ->execute(['category_id' => $id])
            ->fetch();

         if (!empty($webCategory)) {
            // Web category override'ını soft delete yap
            $result = $this->database
               ->table('web_category')
               ->update(['deleted_at'])
               ->where(['category_id'])
               ->prepare()
               ->execute([
                  'category_id' => $id,
                  'deleted_at' => date('Y-m-d H:i:s')
               ]);

            if ($result->affectedRows() <= 0) {
               throw new SystemException('Failed to delete the web category record', 400);
            }
         }

         return true;
      });
   }
}
