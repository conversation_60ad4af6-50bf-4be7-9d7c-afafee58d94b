# SQL
## Bir tablodan koşula göre diğer tabloya veri aktarma
`STOKLAR_ENTEGRASYON` tablosuna `STOKLAR` tablosundan `sto_marka_kodu = 'PIT'` ko<PERSON><PERSON>yla veri eklemek için:
```sql
INSERT INTO STOKLAR_ENTEGRASYON (code)
SELECT sto_kod
FROM STOKLAR
WHERE sto_marka_kodu = 'PIT' AND sto_kod NOT IN (SELECT code FROM STOKLAR_ENTEGRASYON);
```

## Sorgu süresi hesaplama
```sql
DECLARE @EndTime datetime
DECLARE @StartTime datetime 
SELECT @StartTime=GETDATE() 

-- Query

SELECT @EndTime=GETDATE()
SELECT DATEDIFF(SECOND,@StartTime,@EndTime) AS [Milliseconds] 
```

## Mixed Mode Authentication
Bir uygulama doğru `Connection Query` ile bağlanamıyorsa önce `SQL Server and Windows Authentication mode` ayarı kontrol edilir.

```sql
SELECT @@VERSION AS 'SQL Server Version';
```

```cmd
REG ADD "HKLM\Software\Microsoft\Microsoft SQL Server\MSSQL15.MSSQLSERVER\MSSQLServer" /v LoginMode /t REG_DWORD /d 2 /f
```

#### Manuel Yol
- SSM `master` > `Properties`  > `Security` > `SQL Server and Windows Authentication mode`
- SQL Server Configuration Manager `SQL Server Network Configuration` > `Protocols for MSSQLSERVER`
   - Shared Memory `Enabled`
   - Named Pipes `Enabled`
   - TCP/IP `Enabled` > `Ip Adresses` > `IP ALL` TCP PORT `1433`

## Database Alter
Database `Alter` edilemiyorsa Restrict Access seçeneği `SINGLE_USER` yapılır ve gereken işlem yapıldıktan sonra tekrar `MULTI_USER` yapılır.
```sql
USE master;
GO
ALTER DATABASE [DataBase] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
```

## Database Collation
```sql
USE master;  
GO
ALTER DATABASE [DataBase] COLLATE Turkish_CI_AS;
```

## Database List
```sql
USE master;
GO
SELECT name AS 'Database', suser_sname(owner_sid) AS 'Owner' FROM sys.databases
```

## Database Userlist
```sql
select sp.name as login, sp.type_desc as login_type, sl.password_hash, sp.create_date, sp.modify_date,
   case when sp.is_disabled = 1 then 'Disabled' else 'Enabled' end as status
from sys.server_principals sp
left join sys.sql_logins sl
on sp.principal_id = sl.principal_id
where sp.type not in ('G', 'R')
order by sp.name;
```

## Tüm Tablolarda Değer Araması
```sql
DECLARE @SearchValue NVARCHAR(MAX) = '1010';

DECLARE @TableName NVARCHAR(MAX), @ColumnName NVARCHAR(MAX), @Sql NVARCHAR(MAX);

DECLARE table_cursor CURSOR FOR
SELECT 
    t.name AS TableName,
    c.name AS ColumnName
FROM 
    sys.columns c
INNER JOIN 
    sys.tables t ON c.object_id = t.object_id;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName, @ColumnName;

WHILE @@FETCH_STATUS = 0
BEGIN
    SET @Sql = 'IF EXISTS (SELECT 1 FROM MikroDB_V16_GULEM..[' + @TableName + '] WHERE TRY_CAST([' + @ColumnName + '] AS NVARCHAR(MAX)) = ''' + @SearchValue + ''') PRINT ''' + @TableName + ', ' + @ColumnName + '''';
    EXEC sp_executesql @Sql;

    FETCH NEXT FROM table_cursor INTO @TableName, @ColumnName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;
```