# ERP
## <PERSON>ş Kuralları Yönetimi (BRM)
<PERSON>ş kuralları seti, bir organizasyonda uygulanan, organizasyonun amacına ulaşabilmesi için gerekli tanımlamaları, operasyonları ve kısıtlamaları içerir. Günümüzde iş kuralları yönetimini kolaylaştırmak amacı ile birçok uygulama geliştirilmiştir. Bu uygulamalar arasında “ILOG” ve “Fair Isaac” en yaygın kullanılan çözümlerdir.  
Kurumsal uygulamaların çözümleme, tasar<PERSON>m, geliştirme ve test aşamalarında giderek yaygınlaşan bir yaklaşım ise iş kurallarının kural motoru üzerinde tanımlanması ve işletilmesidir. Bu yaklaşım çözümleme aşamasında sözlü olarak ifade edilen iş kurallarının bir kural veri tabanına, genelde bir kural dili ile yazılmasına dayanır. Yazılımın bakım aşamasında iş kuralları değiştiği zaman kural veritabanının kolayca güncellenmesi ile bakım işlerinin büyük bir bölümünün kısa süre içinde tamamlanması avantajına rağmen bu biçimde yazılım geliştirmenin tasarım ve test konularında soru işaretleri bulunmaktadır.

**Neden İş Kuralları Motoru?**  
Sıradan bir yazılım sürecini düşündüğümüzde önce yazılımı gerçekleştirilecek sistemin analizi yapılır. Bu analiz sonucunda iş mantığının ve sistemin ihtiyaçları belirlenir ve bu ihtiyaçların karşılanabileceği tasarım
fazına geçilir. Tasarım sonucunda belirlenen yapıda yazılım yapıldıktan sonra yazılım test edilir ve ortaya çalışan bir ürün çıkmış olur. Burada bir noktaya dikkatinizi çekmek istiyorum. Analizin yapıldığı süre ve
ürünün teslim edildiği süre yazılımın boyutu büyüdükçe artmakta ve bu süre zarfında sürekli olarak ihtiyaçlar değişmektedir. Sizden istenen yazılım siz o yazılımı teslim edene kadar bir sürü güncelleme geçirir. Hatta ürün teslim edildikten sonra da ihtiyaçlar sürekli olarak değişmekte ve eski ihtiyaçlara göre yazılan uygulamalar bazı durumlarda kullanılamaz hale gelmiştir.

Bu önemli problemin çözümü olarak iş yapış şekli ve kodlamanın birbirinden ayrılması düşünülmüş ve böylece kural motorları ortaya çıkmıştır.

**Problem Tanımı**
- İş kuralları sürekli değişir
- Teslim edilmesi planlanan ürün güncelliğini yitirir
- Teslimat süresi uzar
- Yeteri kadar test edilemez
- Ürün kalitesiz olur

**Ne zaman kural motoru uygulanmalıdır?**
- İş akışları kod seviyesinde yönetilemeyecek kadar karışıksa  
   İş akışları karışık olan sistemlerde, bu akışların kodlama seviyesinde yönetilmesi durumunda sistemin geliştirilmesi ve test edilmesi iş akışının karmaşıklığı nedeniyle hem uzun zaman almakta hem de iş akışının her hangi bir aşamasında yapılan ufak bir değişikliğin, sistemin geri kalanında yaratacağı etkileri gözlemlemeyi olanaksız hale getirmektedir. Bu nedenle iş akışı karışık olan sistemlerde kural motoru kullanılması önerilmektedir.
- İş yapış şekli sürekli değişiyorsa
   İkinci olarak iş kuralları sürekli değişen sistemlerde değişen her bir iş kuralı için yeniden kodlama yapılması gerekmekte ve sürekli olarak yeni sürümler alınması ihtiyacı ortaya çıkmaktadır. Sistemin stabilitysinin sağlanması açısından bu tür sistemlerde kural motorları kullanılmalıdır.
- Değişikliklerin hemen uygulamaya alınması gerekiyorsa
   İş kuralları değiştiğinde bu değişikliğin sisteme hemen yansıtılması imkansızdır. Yeni kuralı gerçekleştirebilmek için yeni kodlar yazılmalı ve yeni sürümün test edilmelidir. Bu da zaman kritik sistemlerde işi yavaşlatmaktadır. Bu tür sistemlerde kural motoru kullanmak zaman kayıplarını engeller.

**Kural motoru kullanılması sonucunda**
- İş yapış şekli tanımları kolayca anlaşılır
- İş yapış şekli programlama dillerinden bağımsız olur
- İş yapış şekli bilgisini kodlama mantığından ayırır
- Kaynak kod değiştirilmeden akış değiştirilebilir
- İş yapış şeklindeki değişiklikler hızlıca uygulamaya konur

## İş Süreçleri Yönetimi (BPMS)
**İş süreçlerinizin kurumsal gelişiminizin önünü kesmesine izin vermeyin.**
Rekabette avantajı elde edebilmek için kuruluşlar sürekli olarak bir sonraki “en önemli adım”ın peşindeler. “Bir sonraki uygulama”, “bir sonraki hizmet”, “bir sonraki iş yöntemi”, “bir sonraki teknoloji” …kısacası 
kendilerini rekabette öne çıkartacak, rakiplerinin birkaç adım önüne geçmesini sağlayacak, karlılıklarını arttırırken maliyetlerini düşürecek “bir sonraki daha iyi çözümün” peşindeler.

Ürün yaşam döngülerinin giderek azaldığı ve uluslararası dev rakiplerin pazara kolayca girebildiği bu zor zamanlarda “bir sonraki” arayışı ve sonuçları kurumun hayatta kalabilmesi için büyük önem taşıyor. Bu noktada işin içerisine “iş süreçleri” dahil oluyor.

İş süreçleri, kurumların temel taşları olan iş yapma şekillerini tarif eder. Bir işi gerçekleştirebilmek için yapılması gereken, birbirini izleyen faaliyetler dizisidir. İş süreci karmaşık ya da basit olabileceği gibi insan, makina ya da sistem ile de gerçekleşebilir.

Önde gelen yazılım şirketlerinin sunmuş olduğu iş süreçleri yönetim araçları ; süreç iyileştirme fırsatlarını iyi anlamanızı sağlamanın yanısıra, süreç modelleleri üzerinde birlikte çalışma ve hızlı bir şekilde iş beklentilerinizi aşan çözümler geliştirmede kolaylık sağlıyor. Tüm bunları ise zamandan ve maliyetten tasarruf sağlayarak elde etmenize olanak tanıyor.

**İş Süreçleri Yönetimi (BPM) nedir?**
Bir kurumda iş süreçlerinin detaylı olarak tanımlanması, akışın süreç hedefleri doğrultusunda optimizasyonu, her türlü duruma ilişkin mantıksal modellemenin belirlenmesi, izlenmesi ve iyileştirilmesi stratejik yaklaşımına, “İş Süreçleri Yönetimi” denir.

**Ana ilkeler**
- Bilinmeyen bir süreç modellenemez. İlk aşamada kuruma ait süreçlerin analizlerinin yapılması ve süreç adımlarının açık bir şekilde tanımlanması gerekir.
- Ölçümlenmeyen bir süreç yönetilemez.
- “BPM(iş süreçleri yönetimi)” süreç aktivitelerini ve sonuçlarını ölçümlemeye dayanan, “İYİLEŞTİRME ODAKLI” bir yönetim şeklidir.
- Yönetilmeyen süreç iyileştirilemez. Bir sürecin etkin bir şekilde yönetilmesi için süreç aktivitelerinin ve sonuçlarının ölçümlenebilmesi gerekir.

**Yapılan İşlere Yürütülen Süreçler Olarak Bakmanın Faydaları Nelerdir?**
Süreçlerde gereksiz maliyet yaratan unsurları görmemizi sağlar;
“Süreçlerde gereksiz maliyetleri yaratan temel faktör insanların yavaş çalışması değil, çeşitli nedenlerle işlerin yanlış ya da eksik tamamlanmasıdır.”

Süreçlerde gözlemlenen en önemli gereksiz-maliyet kaynakları;
- Bilgi akışı bozukluklarından kaynaklanan yüksek maliyetli iş görme biçimleri (Yap – Boz Yöntemi)
- Sıra dışı durumlarda ortaya çıkan belirsizlikler nedeniyle iş akışının duraksaması (Süreç Kara Delikleri)
- Gerekli bilgi ve dokümanları bulmak için harcanan zaman (Çok Emek – Az İş)
- İşi kimin yapacağının belli olmaması durumları (Gri Alanlar)

**Sonuç**
Sonuç olarak kurumun sağlıklı bir süreç yönetimi altyapısı oluşturabilmesi için kavramsal iyileştirmenin yanısıra kurumun teknolojik altyapısınında süreç yönetimine imkan verir bir hale getirilmesi gerekmektedir.Bu amaçla uygulanan mimari modellerin en önemlilerinden biri de Servis Odaklı Mimari yani SOA dır.Bir sonraki yazımızda BPM araçları ile SOA nın kurumsal süreçleri iyileştirmedeki rollerini daha detaylı ele alacağız.