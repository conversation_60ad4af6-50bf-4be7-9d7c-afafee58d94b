<template>
   <Container v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ title }}</v-card-title>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               prepend-icon="$save"
               @click="form?.requestSubmit()">
               {{ t("app.save") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
            <LanguageTab
               v-model="language"
               v-bind:loading="isLoading" />
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.code") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-input class="text-sm select-all">{{ category.code }}</v-input>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="category.title"
                     v-bind:rules="[appRules.required()]">
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="category.title" />
                     </template>
                  </v-text-field>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-input class="max-h-40 overflow-y-auto text-sm whitespace-pre-line select-all">{{ category.content }}</v-input>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch
                     v-model="category.is_active"
                     v-bind:false-value="0"
                     v-bind:ripple="false"
                     v-bind:true-value="1"
                     color="primary"
                     density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ category.is_active ? t("app.active") : t("app.passive") }}</div>
                     </template>
                  </v-switch>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.image") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <ImageList v-bind:items="[category.image_path]" />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>

      <Card>
         <template v-slot:extension>
            <v-card-title class="text-base">Bağlantılar</v-card-title>
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.url") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="category.url"
                     v-bind:rules="[appRules.required()]">
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="category.title" />
                     </template>
                  </v-text-field>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("website.metaTitle") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field v-model="category.meta_title" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("website.metaDescription") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea
                     v-model="category.meta_description"
                     v-bind:rules="[appRules.required()]"
                     class="[&_.v-field\_\_input]:min-h-[min(var(--v-input-control-height,56px),364px)]"
                     auto-grow
                     no-resize>
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="category.meta_description" />
                     </template>
                  </v-textarea>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("website.metaKeywords") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field v-model="category.meta_keywords" />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import ImageList from "@/components/Input/ImageList.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { useCreateCategory, useGetCategoryById, useOverrideCategory } from "../services/CategoryService";
import { ICategory, ICategoryStore } from "../utils/types";

const { t } = useI18n();
const route = useRoute() as IRoute;
const snackbar = useMessageStore();
const appStore = useAppStore();

// category
const form = ref<HTMLFormElement>();
const category = ref({
   is_active: 1
} as ICategory);
const routeId = computed(() => route.params.id);
const isCreate = computed(() => routeId.value === "create");
const isEnabled = computed(() => !!routeId.value && !isCreate.value);
const language = ref(1);
const title = computed(() => (isCreate.value ? t("inventory.createCategory") : t("inventory.categoryDetail")));

// set breadcrumb
appStore.setBreadcrumb("CategoryDetail", title);

// services
const getCategoryById = useGetCategoryById({
   id: routeId,
   enabled: isEnabled,
   language: language,
   onSuccess: (data) => {
      category.value = { ...data };
   }
});
const overrideCategory = useOverrideCategory();
const createCategory = useCreateCategory();
// const { data: categoryData, isLoading: categoryLoading } = useGetCategoryAll();
// const categoryAll = computed(() => categoryData.value?.filter((item) => item.id !== category.value.id));

// loading
const isLoading = computed(() => getCategoryById.isLoading.value);
const isPending = computed(() => createCategory.isPending.value || overrideCategory.isPending.value);

// handlers
const formHandler = async () => {
   if (!form.value?.isValid) return;

   const payload: ICategoryStore = {
      code: category.value.code,
      category_id: category.value.id,
      // parent_id: category.value.parent_id,
      translate: [
         {
            language_id: language.value,
            title: category.value.title,
            url: category.value.url,
            meta_title: category.value.meta_title,
            meta_description: category.value.meta_description,
            meta_keywords: category.value.meta_keywords
         }
      ],
      is_active: category.value.is_active
   };

   try {
      if (isCreate.value) {
         const response = await createCategory.mutateAsync(payload);
         snackbar.add({ text: t("app.recordCreated") });
         router.push("/category/" + response.data.id);
      } else {
         // create or update
         await overrideCategory.mutateAsync(payload);
         snackbar.add({ text: t("app.recordUpdated") });
      }
   } catch (error) {
      snackbar.add({ text: t("app.recordFailed"), color: "error" });
   }
};
</script>
