<?php

declare(strict_types=1);

use System\Migration\Migration;

class web_standard extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `web_standard` (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `standard_id` INT NOT NULL DEFAULT 0,
         `image_path` VARCHAR(250) NULL DEFAULT NULL,
         `sort_order` INT NOT NULL DEFAULT 0,
         {$this->defaults()}
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `web_standard`");
   }
}
