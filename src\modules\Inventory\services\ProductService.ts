import { IProduct, IProductStore } from "../utils/types";

export const useGetProductAll = (payload?: IQuery<IProduct[]>) => {
   const options = computed(() => ({
      queryKey: ["product", "productAll"],
      queryFn: async () => {
         return (await appAxios.get("/inventory/product/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetProductById = (payload?: { id?: MaybeRef<string> } & IQuery<IProduct>) => {
   const options = computed(() => ({
      queryKey: ["product", "productById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/inventory/product/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateProduct = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["product", "updateProduct"],
      mutationFn: async (data: IProductStore): Promise<IResponse<IProduct>> => {
         return (await appAxios.put("/inventory/product/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useCreateProduct = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["product", "createProduct"],
      mutationFn: async (data: IProductStore): Promise<IResponse<IProduct>> => {
         return (await appAxios.post("/inventory/product/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};
