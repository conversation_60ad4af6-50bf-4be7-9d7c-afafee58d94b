<template>
   <Container v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ t("inventory.manufacturerList") }}</v-card-title>
            <ActionButton
               v-bind:disabled="isLoading"
               :to="$route.path + '/create'">
               {{ t("app.add") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <SearchInput v-model:search="filter" />
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data"
            v-bind:row-click="(item: IManufacturer) => $router.push(`${$route.path}/${item.id}`)">
            <template v-slot:item.is_active="{ value }">
               <v-chip
                  v-bind:color="value ? 'primary' : undefined"
                  class="text-xs"
                  density="compact">
                  {{ value ? t("app.active") : t("app.passive") }}
               </v-chip>
            </template>
         </DataTable>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { useGetManufacturerAll } from "../services/ManufacturerService";
import { IManufacturer } from "../utils/types";

const { t } = useI18n();

const filter = ref("");
const selected = ref([]);
const headers = computed((): IHeader<IManufacturer>[] => [
   { title: t("app.code"), key: "code", width: "100" },
   { title: t("app.title"), key: "title" },
   { title: t("app.status"), key: "is_active", width: "150" },
   { title: t("app.createDate"), key: "created_at", width: "250", format: true },
   { title: t("app.updateDate"), key: "updated_at", width: "250", format: true }
]);

const { data, isLoading } = useGetManufacturerAll();
</script>
