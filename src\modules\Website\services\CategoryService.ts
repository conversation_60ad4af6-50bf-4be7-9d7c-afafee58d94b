import { ICategory, ICategoryStore } from "../utils/types";

export const useGetCategoryAll = (payload?: IQuery<ICategory[]>) => {
   const options = computed(() => ({
      queryKey: ["category", "website,", "categoryAll"],
      queryFn: async () => {
         return (await appAxios.get("/website/category/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetCategoryById = (payload?: { id?: MaybeRef<string> } & IQuery<ICategory>) => {
   const options = computed(() => ({
      queryKey: ["category", "website", "categoryById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/website/category/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useOverrideCategory = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["category", "website", "overrideCategory"],
      mutationFn: async (data: ICategoryStore): Promise<IResponse<ICategory>> => {
         return (await appAxios.put("/website/category/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["category", "website"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useCreateCategory = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["category", "website", "createCategory"],
      mutationFn: async (data: ICategoryStore): Promise<IResponse<ICategory>> => {
         return (await appAxios.post("/website/category/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["category", "website"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};
