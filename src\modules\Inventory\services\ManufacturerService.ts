import { IManufacturer, IManufacturerStore } from "../utils/types";

export const useGetManufacturerAll = (payload?: IQuery<IManufacturer[]>) => {
   const options = computed(() => ({
      queryKey: ["manufacturer", "manufacturerAll", payload?.params?.competing],
      queryFn: async () => {
         return (await appAxios.get("/inventory/manufacturer/", { params: { ...payload?.params } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetManufacturerById = (payload?: { id?: MaybeRef<string> } & IQuery<IManufacturer>) => {
   const options = computed(() => ({
      queryKey: ["manufacturer", "manufacturerById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/inventory/manufacturer/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateManufacturer = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["manufacturer", "updateManufacturer"],
      mutationFn: async (data: IManufacturerStore): Promise<IResponse<IManufacturer>> => {
         return (await appAxios.put("/inventory/manufacturer/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["manufacturer"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useCreateManufacturer = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["manufacturer", "createManufacturer"],
      mutationFn: async (data: IManufacturerStore): Promise<IResponse<IManufacturer>> => {
         return (await appAxios.post("/inventory/manufacturer/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["manufacturer"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};
