<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Controllers;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Inventory\DTOs\CategoryDTO;
use App\Modules\Inventory\Services\CategoryService;

/**
 * @OA\Tag(name="Category", description="Kategori işlemleri")
 */
class CategoryController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected CategoryService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Category"}, path="/category/", summary="Kategori listesi",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getAllCategory() {
      $this->response(function () {
         $result = $this->service->getAllCategory($this->language());
         return $result;
      });
   }

   /**
    * @OA\Get(tags={"Category"}, path="/category/{id}", summary="Kategori detayı (ID'ye göre)",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getCategoryById(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getCategory($id, $this->language());
         return $result;
      });
   }

   /**
    * @OA\Post(tags={"Category"}, path="/category/", summary="Kategori ekle",
    *    @OA\Response(response=201, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"code", "image_path", "is_active", "sort_order", "parent_id", "group_id"},
    *       @OA\Property(property="code", type="string", example="CAT001"),
    *       @OA\Property(property="image_path", type="string", example="/images/categories/category1.jpg"),
    *       @OA\Property(property="is_active", type="boolean", example=true),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="parent_id", type="integer", example=0),
    *       @OA\Property(property="group_id", type="integer", example=1),
    *       @OA\Property(property="translate", type="array", @OA\Items(
    *          @OA\Property(property="language_id", type="integer", example="1"),
    *          @OA\Property(property="title", type="string", example="Kategori Başlığı"),
    *          @OA\Property(property="content", type="string", example="Kategori Açıklaması")
    *       ))
    *    ))
    * )
    */
   public function createCategory() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new CategoryDTO();
         $dto->fromRequest($request);
         $result = $this->service->createCategory($dto, $this->language());
         return $result;
      }, code: 201);
   }

   /**
    * @OA\Put(tags={"Category"}, path="/category/", summary="Kategori güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"id", "code", "image_path", "is_active", "sort_order", "parent_id", "group_id"},
    *       @OA\Property(property="id", type="integer", example=1),
    *       @OA\Property(property="code", type="string", example="CAT001"),
    *       @OA\Property(property="image_path", type="string", example="/images/categories/category1.jpg"),
    *       @OA\Property(property="is_active", type="boolean", example=true),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="parent_id", type="integer", example=0),
    *       @OA\Property(property="group_id", type="integer", example=1),
    *       @OA\Property(property="translate", type="array", @OA\Items(
    *          @OA\Property(property="language_id", type="integer"),
    *          @OA\Property(property="title", type="string"),
    *          @OA\Property(property="content", type="string")
    *       ),
    *       example={{
    *          "language_id": 1,
    *          "title": "Kategori Başlığı",
    *          "content": "Kategori Açıklaması"
    *       },{
    *          "language_id": 2,
    *          "title": "Category Title",
    *          "content": "Category Description"
    *       }})
    *    ))
    * )
    */
   public function updateCategory() {
      return $this->response(function () {
         $request = $this->request->json();
         $dto = new CategoryDTO();
         $dto->fromRequest($request);
         $result = $this->service->updateCategory($dto, $this->language());
         return $result;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Category"}, path="/category/{id}", summary="Kategori sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteCategory(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete(['id' => $id]);
         return $result;
      });
   }
}
