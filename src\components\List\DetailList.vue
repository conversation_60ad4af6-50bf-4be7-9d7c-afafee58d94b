<template>
   <v-list
      class="mb-4 max-w-full p-0"
      density="default"
      v-bind:slim="false"
      lines="two">
      <v-list-item
         v-for="item in props.data"
         v-bind:active="false"
         v-bind:key="item.id"
         v-bind:ripple="false"
         v-bind:subtitle="(item[props.subtitle as keyof typeof item] as string)"
         v-bind:title="(item[props.title as keyof typeof item] as string)"
         v-bind:to="props.to ? props.to + item.id : undefined"
         class="[&:last-child]:mb-0 [&:not(:last-child)]:mb-2"
         border
         ellipsis>

         <template v-slot:prepend>
            <v-avatar
               density="default"
               rounded>
               <v-img
                  v-if="item.image_path || item.image_list?.length"
                  v-bind:src="VITE_MEDIA + (item.image_path || item.image_list?.sort((a, b) => a.sort_order - b.sort_order)[0]?.image_path)"
                  cover />
               <v-icon
                  v-else
                  icon="$question"
                  size="large" />
            </v-avatar>
         </template>

         <template v-slot:append>
            <v-btn
               icon="$close"
               @click.stop.prevent="props.delete(item.id)" />
         </template>
      </v-list-item>
   </v-list>
</template>

<script lang="ts" setup>
const props = defineProps({
   data: {
      type: [Array<{
         id: number;
         title: string;
         content?: string;
         value?: string;
         image_path?: string;
         image_list?: {
            id: number;
            image_path: string;
            sort_order: number
         }[]
      }>, null],
      default: null
   },
   delete: {
      type: Function,
      required: true
   },
   to: {
      type: String
   },
   title: {
      type: String,
      default: "title",
   },
   subtitle: {
      type: String,
      default: "content",
   }
});
</script>
