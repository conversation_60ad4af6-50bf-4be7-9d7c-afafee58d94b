<template>
   <v-btn
      v-bind:loading="isLoading"
      v-bind:ripple="false"
      density="compact"
      icon="$copy"
      size="small"
      variant="plain"
      @click="copyHandler" />
</template>

<script lang="ts" setup>
const model = defineModel({ type: String, default: "" });
const isLoading = ref(false);
const snackbar = useMessageStore();

const copyHandler = async () => {
   isLoading.value = true;
   try {
      await navigator.clipboard.writeText(model.value);
      snackbar.add({ text: "Kopyalandı" });
   } catch (error) {
      snackbar.add({ text: "Kopyala<PERSON> başarısız", color: "error" });
   } finally {
      isLoading.value = false;
   }
};
</script>
