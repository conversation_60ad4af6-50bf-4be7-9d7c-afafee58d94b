# PowerShell
### 7-Zip Son Sürü<PERSON>ü<PERSON><PERSON>
```powershell
$dlurl = 'https://7-zip.org/' + (Invoke-WebRequest -UseBasicParsing -Uri 'https://7-zip.org/' | Select-Object -ExpandProperty Links | Where-Object {($_.outerHTML -match 'Download')-and ($_.href -like "a/*") -and ($_.href -like "*-x64.exe")} | Select-Object -First 1 | Select-Object -ExpandProperty href)
# modified to work without IE
# above code from: https://perplexity.nl/windows-powershell/installing-or-updating-7-zip-using-powershell/
$installerPath = Join-Path $env:TEMP (Split-Path $dlurl -Leaf)
Invoke-WebRequest $dlurl -OutFile $installerPath
Start-Process -FilePath $installerPath -Args "/S" -Verb RunAs -Wait
Remove-Item $installerPath
```

### F2 Enter Tuşu ile Enter Tuşu Otomatik Tetiklenmesi
Excelde formüllü alanlarda hesaplamalar takılı kalabiliyor.

```powershell
Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;

public class Keyboard {
    [DllImport("user32.dll", SetLastError = true)]
    public static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, IntPtr dwExtraInfo);

    public const int KEYEVENTF_KEYUP = 0x0002;
}
"@ -PassThru

function f2enter {
    Write-Host "Başlatılıyor... Durdurmak için Ctrl + C tuşlarına bas."

    $milliseconds = 50

    while ($true) { 
        [Keyboard]::keybd_event(0x71, 0, 0, [IntPtr]::Zero) # F2 (VK_F2 = 0x71)
        Start-Sleep -Milliseconds $milliseconds
        [Keyboard]::keybd_event(0x71, 0, [Keyboard]::KEYEVENTF_KEYUP, [IntPtr]::Zero) # F2 bırak
        Start-Sleep -Milliseconds $milliseconds

        [Keyboard]::keybd_event(0x0D, 0, 0, [IntPtr]::Zero) # Enter (VK_RETURN = 0x0D)
        Start-Sleep -Milliseconds $milliseconds
        [Keyboard]::keybd_event(0x0D, 0, [Keyboard]::KEYEVENTF_KEYUP, [IntPtr]::Zero) # Enter bırak

        Start-Sleep -Milliseconds $milliseconds
    }
}
```