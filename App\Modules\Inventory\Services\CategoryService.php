<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Services;

use System\Upload\Upload;
use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Inventory\DTOs\CategoryDTO;
use App\Modules\Inventory\Models\CategoryModel;
use App\Modules\Inventory\Repositories\CategoryRepository;

class CategoryService extends BaseService {
   /** @var CategoryRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Upload $upload,
      protected Validation $validation,
      CategoryRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function getAllCategory(int $lang_id): array {
      $result = $this->repository->findAll($lang_id);
      return array_map(function ($item) {
         $category = new CategoryModel();
         $category->fromRequest($item);

         return $category;
      }, $result);
   }

   public function getCategory(int $id, int $lang_id): CategoryModel {
      $result = $this->repository->findOne($id, $lang_id);
      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      $category = new CategoryModel();
      $category->fromRequest($result);

      return $category;
   }

   public function createCategory(CategoryDTO $dto, int $lang_id): CategoryModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'code' => 'required',
            'image_path' => 'nullable',
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'parent_id' => 'required|numeric',
            'group_id' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required',
         ]);

         $id = $this->create([
            'code' => $dto->code,
            'image_path' => $dto->image_path,
            'is_active' => $dto->is_active,
            'sort_order' => $dto->sort_order,
            'parent_id' => $dto->parent_id,
            'group_id' => $dto->group_id,
         ]);

         $this->translate($dto->translate, [
            'category_id' => $id
         ], 'category_translate');

         return $this->getCategory($id, $lang_id);
      });
   }

   public function updateCategory(CategoryDTO $dto, int $lang_id): CategoryModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'id' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required',
         ]);

         $this->update($dto, [
            'code' => $dto->code,
            'image_path' => $dto->image_path,
            'is_active' => $dto->is_active,
            'sort_order' => $dto->sort_order,
            'parent_id' => $dto->parent_id,
            'group_id' => $dto->group_id,
         ]);

         $this->translate($dto->translate, [
            'category_id' => $dto->id,
         ], 'category_translate');

         return $this->getCategory($dto->id, $lang_id);
      });
   }
}
