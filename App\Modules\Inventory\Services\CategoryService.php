<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Services;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use App\Modules\Inventory\DTOs\CategoryDTO;
use App\Modules\Inventory\Models\CategoryModel;
use App\Modules\Inventory\Repositories\CategoryRepository;

class CategoryService extends BaseService {
   /** @var CategoryRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      CategoryRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function createCategory(CategoryDTO $dto, int $lang_id): CategoryModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'code' => 'required',
            'image_path' => 'nullable',
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'parent_id' => 'required|numeric',
            'group_id' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required',
         ]);

         $id = $this->create([
            'code' => $dto->code,
            'image_path' => $dto->image_path,
            'is_active' => $dto->is_active,
            'sort_order' => $dto->sort_order,
            'parent_id' => $dto->parent_id,
            'group_id' => $dto->group_id,
         ]);

         $this->translate($dto->translate, [
            'category_id' => $id
         ], 'category_translate');

         return $this->getOne($id, $lang_id, CategoryModel::class);
      });
   }

   public function updateCategory(CategoryDTO $dto, int $lang_id): CategoryModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'id' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required',
         ]);

         $this->update($dto, [
            'code' => $dto->code,
            'image_path' => $dto->image_path,
            'is_active' => $dto->is_active,
            'sort_order' => $dto->sort_order,
            'parent_id' => $dto->parent_id,
            'group_id' => $dto->group_id,
         ]);

         $this->translate($dto->translate, [
            'category_id' => $dto->id,
         ], 'category_translate');

         return $this->getOne($dto->id, $lang_id, CategoryModel::class);
      });
   }
}
