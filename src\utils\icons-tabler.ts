import {
   IconAdjustments,
   IconAlertSquareRoundedFilled,
   IconBell,
   IconBox,
   IconBrowser,
   IconBuildingWarehouse,
   IconCalendar,
   IconCaretDownFilled,
   IconCaretRightFilled,
   IconCheck,
   IconChevronDown,
   IconChevronLeft,
   IconChevronLeftPipe,
   IconChevronRight,
   IconChevronRightPipe,
   IconChevronUp,
   IconCircle,
   IconCircleCheckFilled,
   IconCircleDotFilled,
   IconCircleFilled,
   IconCircleX,
   IconCloudUpload,
   IconColorPicker,
   IconCopy,
   IconDeviceFloppy,
   IconDeviceIpadHorizontal,
   IconDeviceIpadHorizontalQuestion,
   IconDevices,
   IconDotsVertical,
   IconInfoSquareRoundedFilled,
   IconLanguage,
   IconListDetails,
   IconLogin,
   IconLogout,
   IconMenu2,
   IconMinus,
   IconMoonStars,
   IconPaperclip,
   IconPencil,
   IconPlus,
   IconQuestionMark,
   IconRefresh,
   IconReplace,
   IconSearch,
   IconSelector,
   IconSettings,
   IconShare,
   IconSlash,
   IconSortAscending,
   IconSortDescending,
   IconSparkles,
   IconSquareRounded,
   IconSquareRoundedCheckFilled,
   IconSquareRoundedMinusFilled,
   IconStar,
   IconStarFilled,
   IconStarHalfFilled,
   IconSun,
   IconTie,
   IconTrash,
   IconUser,
   IconUserCog,
   IconUserMinus,
   IconUserPlus,
   IconX
} from "@tabler/icons-vue";

const tablerAliases = {
   // vuetify default
   calendar: IconCalendar,
   cancel: IconCircleX,
   checkboxIndeterminate: IconSquareRoundedMinusFilled,
   checkboxOff: IconSquareRounded,
   checkboxOn: IconSquareRoundedCheckFilled,
   clear: IconX,
   close: IconX,
   collapse: IconChevronUp,
   complete: IconCheck,
   delete: IconCircleX,
   delimiter: IconCircleFilled,
   dropdown: IconCaretDownFilled,
   edit: IconPencil,
   error: IconCircleX,
   expand: IconChevronDown,
   eyeDropper: IconColorPicker,
   file: IconPaperclip,
   first: IconChevronLeftPipe,
   info: IconInfoSquareRoundedFilled,
   last: IconChevronRightPipe,
   loading: IconRefresh,
   menu: IconMenu2,
   minus: IconMinus,
   next: IconChevronRight,
   plus: IconPlus,
   prev: IconChevronLeft,
   radioOff: IconCircle,
   radioOn: IconCircleDotFilled,
   ratingEmpty: IconStar,
   ratingFull: IconStarFilled,
   ratingHalf: IconStarHalfFilled,
   sortAsc: IconSortAscending,
   sortDesc: IconSortDescending,
   subgroup: IconCaretDownFilled,
   success: IconCircleCheckFilled,
   treeviewCollapse: IconCaretDownFilled,
   treeviewExpand: IconCaretRightFilled,
   unfold: IconSelector,
   upload: IconCloudUpload,
   warning: IconAlertSquareRoundedFilled,
   sparkles: IconSparkles,
   save: IconDeviceFloppy,
   question: IconQuestionMark,

   notification: IconBell,
   slash: IconSlash,
   search: IconSearch,
   dots: IconDotsVertical,
   translate: IconLanguage,
   settings: IconSettings,
   trash: IconTrash,
   copy: IconCopy,

   // account
   accountProfile: IconUser,
   accountAdd: IconUserPlus,
   accountRemove: IconUserMinus,
   accountSettings: IconUserCog,
   accountLogin: IconLogin,
   accountLogout: IconLogout,

   // theme
   themeDark: IconMoonStars,
   themeLight: IconSun,

   // arrow
   chevronRight: IconChevronRight,
   chevronLeft: IconChevronLeft,

   // modules
   inventory: IconBox,
   website: IconBrowser,
   // dealer: IconCircles,
   // dealer: IconCategoryPlus,
   // dealer: IconGitBranch,
   dealer: IconReplace,
   crm: IconListDetails,
   warehouse: IconBuildingWarehouse,
   employee: IconTie,
   socialMedia: IconShare,
   devices: IconDevices,

   product: IconDeviceIpadHorizontal,
   competing: IconDeviceIpadHorizontalQuestion,
   definitions: IconAdjustments
};

const tabler = {
   component: (props: IconProps) => {
      return h(props.tag, [h(tablerAliases[props.icon as keyof typeof tablerAliases])]);
   }
};

export { tabler, tablerAliases };

