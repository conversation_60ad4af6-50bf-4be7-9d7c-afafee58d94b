<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class ImageRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'image'
   ) {
      $this->database->prefix('inv_');
   }

   public function findAll(int $lang_id): array {
      return [];
   }

   public function findOne(int $id, int $lang_id): array|false {
      return [];
   }
}
