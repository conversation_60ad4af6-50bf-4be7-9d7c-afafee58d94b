# Git

## npm yarn commands

| İşlem | npm | yarn |
|-------|-----|------|
| Install dependencies | `npm install` | `yarn` |
| Install a package | `npm install [package_name]` | `yarn add [package_name]` |
| Install a package globally | `npm install -g [package_name]` | `yarn global add [package_name]` |
| Install a package as a development dependency | `npm install --save-dev [package_name]` | `yarn add --dev [package_name]` |
| Uninstall a package | `npm uninstall [package_name]` | `yarn remove [package_name]` |
| Uninstall a package globally | `npm uninstall -g [package_name]` | `yarn global remove [package_name]` |
| Uninstall a development dependency package | `npm uninstall --save-dev [package_name]` | `yarn remove [package_name]` |
| Update the dependencies | `npm update` | `yarn upgrade` |
| Update a package | `npm update [package_name]` | `yarn upgrade [package_name]` |
| Create a new package | `npm init` | `yarn init` |
| Run a script defined in the package.json | `npm run` | `yarn run` |
| Test a package | `npm test` | `yarn test` |
| Publish a package | `npm publish` | `yarn publish` |
| Remove all data from the cache | `npm cache clean` | `yarn cache clean` |

## submodule

```bash
git submodule add https://github.com/user/repo.git target/folder
```
