<?php

declare(strict_types=1);

namespace App\Modules\Website\Services;

use System\Upload\Upload;
use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Website\DTOs\ProductDTO;
use App\Modules\Website\Models\ProductModel;
use App\Modules\Website\Repositories\ProductRepository;

class ProductService extends BaseService {
   /** @var ProductRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      protected Upload $upload,
      ProductRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function getAllProduct(int $lang_id): array {
      $result = $this->repository->findAll($lang_id);
      return array_map(function ($item) use ($lang_id) {
         $product = new ProductModel();
         $product->fromRequest($item);

         // $product->category_list = $this->repository->findCategory($product->id, $lang_id);
         // $product->manufacturer_list = $this->repository->findManufacturer($product->id, $lang_id);
         // $product->attr_list = $this->repository->findAttr($product->id, $lang_id);
         // $product->standard_list = $this->repository->findStandard($product->id, $lang_id);
         // $product->image_list = $this->repository->findImage($product->id);

         return $product;
      }, $result);
   }

   public function getProduct(int $id, int $lang_id): ProductModel {
      $result = $this->repository->findOne($id, $lang_id);
      if (empty($result)) {
         throw new SystemException('Product not found', 404);
      }

      $product = new ProductModel();
      $product->fromRequest($result);

      $product->category_list = $this->repository->findCategory($product->id, $lang_id);
      $product->manufacturer_list = $this->repository->findManufacturer($product->id, $lang_id);
      $product->attr_list = $this->repository->findAttr($product->id, $lang_id);
      $product->standard_list = $this->repository->findStandard($product->id, $lang_id);
      $product->image_list = $this->repository->findImage($product->id);

      return $product;
   }

   public function getProductByUrl(string $url, int $lang_id): ProductModel {
      $result = $this->repository->findByUrl($url, $lang_id);
      if (empty($result)) {
         throw new SystemException('Product not found', 404);
      }

      $product = new ProductModel();
      $product->fromRequest($result);

      $product->category_list = $this->repository->findCategory($product->id, $lang_id);
      $product->manufacturer_list = $this->repository->findManufacturer($product->id, $lang_id);
      $product->attr_list = $this->repository->findAttr($product->id, $lang_id);
      $product->standard_list = $this->repository->findStandard($product->id, $lang_id);
      $product->image_list = $this->repository->findImage($product->id);

      return $product;
   }

   public function getProductsByCategory(int $category_id, int $lang_id, int $limit = 10, int $offset = 0): array {
      $result = $this->repository->findByCategoryId($category_id, $lang_id, $limit, $offset);
      return array_map(function ($item) use ($lang_id) {
         $product = new ProductModel();
         $product->fromRequest($item);

         $product->category_list = $this->repository->findCategory($product->id, $lang_id);
         $product->manufacturer_list = $this->repository->findManufacturer($product->id, $lang_id);
         $product->attr_list = $this->repository->findAttr($product->id, $lang_id);
         $product->standard_list = $this->repository->findStandard($product->id, $lang_id);
         $product->image_list = $this->repository->findImage($product->id);

         return $product;
      }, $result);
   }

   public function searchProducts(string $query, int $lang_id, int $limit = 10, int $offset = 0): array {
      $result = $this->repository->search($query, $lang_id, $limit, $offset);
      return array_map(function ($item) use ($lang_id) {
         $product = new ProductModel();
         $product->fromRequest($item);

         $product->category_list = $this->repository->findCategory($product->id, $lang_id);
         $product->manufacturer_list = $this->repository->findManufacturer($product->id, $lang_id);
         $product->attr_list = $this->repository->findAttr($product->id, $lang_id);
         $product->standard_list = $this->repository->findStandard($product->id, $lang_id);
         $product->image_list = $this->repository->findImage($product->id);

         return $product;
      }, $result);
   }

   public function createProduct(ProductDTO $dto, int $lang_id): ProductModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'id' => 'required|numeric', // Inventory product ID gerekli
            'code' => 'required',
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'product_category' => 'must_be_array',
            'product_manufacturer' => 'must_be_array',
            'product_attr' => 'must_be_array',
            'product_standard' => 'must_be_array',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required',
            'url' => 'required',
         ]);

         // Inventory'de ürün var mı kontrol et
         $invProduct = $this->database
            ->table('inv_product')
            ->select()
            ->where(['id'])
            ->prepare()
            ->execute(['id' => $dto->id])
            ->fetch();

         if (empty($invProduct)) {
            throw new SystemException('Inventory product not found', 404);
         }

         // Web_product tablosuna override verisi ekle (product_id ile)
         $result = $this->database
            ->table('web_product')
            ->insert(['product_id', 'code', 'is_active', 'sort_order'])
            ->prepare()
            ->execute([
               'product_id' => $dto->id, // Inventory product ID'si
               'code' => $dto->code,
               'is_active' => $dto->is_active,
               'sort_order' => $dto->sort_order,
            ]);

         if ($result->affectedRows() <= 0) {
            throw new SystemException('Failed to create the web product record', 400);
         }

         // Web_product_translate tablosuna çeviri ekle
         $this->database
            ->table('web_product_translate')
            ->insert(['product_id', 'language_id', 'title', 'content', 'url', 'meta_title', 'meta_description', 'meta_keywords'])
            ->prepare()
            ->execute([
               'product_id' => $dto->id, // Inventory product ID'si
               'language_id' => $dto->translate['language_id'],
               'title' => $dto->translate['title'],
               'content' => $dto->translate['content'],
               'url' => $dto->translate['url'],
               'meta_title' => $dto->translate['meta_title'] ?? null,
               'meta_description' => $dto->translate['meta_description'] ?? null,
               'meta_keywords' => $dto->translate['meta_keywords'] ?? null,
            ]);

         $this->createRelation($dto, $dto->id);

         if (isset($dto->image_path_list) && is_array($dto->image_path_list)) {
            foreach ($dto->image_path_list as $index => $path) {
               $this->database
                  ->table('web_product_image')
                  ->insert(['product_id', 'image_path', 'sort_order'])
                  ->prepare()
                  ->execute([
                     'product_id' => $dto->id, // Inventory product ID'si
                     'image_path' => $path,
                     'sort_order' => $index + 1
                  ]);
            }
         }

         return $this->getProduct($dto->id, $lang_id);
      });
   }

   public function updateProduct(ProductDTO $dto, int $lang_id): ProductModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         // Inventory'de ürün var mı kontrol et
         $invProduct = $this->database
            ->table('inv_product')
            ->select()
            ->where(['id'])
            ->prepare()
            ->execute(['id' => $dto->id])
            ->fetch();

         if (empty($invProduct)) {
            throw new SystemException('Inventory product not found', 404);
         }

         $this->validate($dto->toArray(), [
            'id' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required',
            'url' => 'required',
         ]);

         // Web_product tablosunda override var mı kontrol et (product_id ile)
         $webProduct = $this->database
            ->table('web_product')
            ->select()
            ->where(['product_id'])
            ->prepare()
            ->execute(['product_id' => $dto->id])
            ->fetch();

         if (empty($webProduct)) {
            // Web_product'ta yoksa oluştur
            $this->database
               ->table('web_product')
               ->insert(['product_id', 'code', 'is_active', 'sort_order'])
               ->prepare()
               ->execute([
                  'product_id' => $dto->id,
                  'code' => $dto->code,
                  'is_active' => $dto->is_active,
                  'sort_order' => $dto->sort_order,
               ]);
         } else {
            // Web_product'ta varsa güncelle
            $this->database
               ->table('web_product')
               ->update(['code', 'is_active', 'sort_order'])
               ->where(['product_id'])
               ->prepare()
               ->execute([
                  'product_id' => $dto->id,
                  'code' => $dto->code,
                  'is_active' => $dto->is_active,
                  'sort_order' => $dto->sort_order,
               ]);
         }

         // Web_product_translate güncelle/oluştur
         $webTranslate = $this->database
            ->table('web_product_translate')
            ->select()
            ->where(['product_id', 'language_id'])
            ->prepare()
            ->execute([
               'product_id' => $dto->id,
               'language_id' => $dto->translate['language_id']
            ])
            ->fetch();

         if (empty($webTranslate)) {
            // Çeviri yoksa oluştur
            $this->database
               ->table('web_product_translate')
               ->insert(['product_id', 'language_id', 'title', 'content', 'url', 'meta_title', 'meta_description', 'meta_keywords'])
               ->prepare()
               ->execute([
                  'product_id' => $dto->id,
                  'language_id' => $dto->translate['language_id'],
                  'title' => $dto->translate['title'],
                  'content' => $dto->translate['content'],
                  'url' => $dto->translate['url'],
                  'meta_title' => $dto->translate['meta_title'] ?? null,
                  'meta_description' => $dto->translate['meta_description'] ?? null,
                  'meta_keywords' => $dto->translate['meta_keywords'] ?? null,
               ]);
         } else {
            // Çeviri varsa güncelle
            $this->database
               ->table('web_product_translate')
               ->update(['title', 'content', 'url', 'meta_title', 'meta_description', 'meta_keywords'])
               ->where(['product_id', 'language_id'])
               ->prepare()
               ->execute([
                  'product_id' => $dto->id,
                  'language_id' => $dto->translate['language_id'],
                  'title' => $dto->translate['title'],
                  'content' => $dto->translate['content'],
                  'url' => $dto->translate['url'],
                  'meta_title' => $dto->translate['meta_title'] ?? null,
                  'meta_description' => $dto->translate['meta_description'] ?? null,
                  'meta_keywords' => $dto->translate['meta_keywords'] ?? null,
               ]);
         }

         $this->updateRelation($dto, [
            'product_category',
            'product_manufacturer',
            'product_attr',
            'product_standard'
         ]);

         if (isset($dto->image_path_list) && is_array($dto->image_path_list)) {
            // Önce mevcut web resimleri sil
            $this->database
               ->table('web_product_image')
               ->delete()
               ->where(['product_id'])
               ->prepare()
               ->execute(['product_id' => $dto->id]);

            // Yeni resimleri ekle
            foreach ($dto->image_path_list as $index => $path) {
               $this->database
                  ->table('web_product_image')
                  ->insert(['product_id', 'image_path', 'sort_order'])
                  ->prepare()
                  ->execute([
                     'product_id' => $dto->id,
                     'image_path' => $path,
                     'sort_order' => $index + 1
                  ]);
            }
         }

         return $this->getProduct($dto->id, $lang_id);
      });
   }

   private function createRelation(ProductDTO $dto, int $id): void {
      if (isset($dto->product_category) && is_array($dto->product_category)) {
         foreach ($dto->product_category as $category_id) {
            $category = $this->database
               ->table('web_product_category')
               ->insert(['product_id', 'category_id'])
               ->prepare()
               ->execute([
                  'product_id' => $id,
                  'category_id' => $category_id
               ]);

            if ($category->affectedRows() <= 0) {
               throw new SystemException('Category relation not created', 400);
            }
         }
      }

      if (isset($dto->product_manufacturer) && is_array($dto->product_manufacturer)) {
         foreach ($dto->product_manufacturer as $manufacturer_id) {
            $manufacturer = $this->database
               ->table('web_product_manufacturer')
               ->insert(['product_id', 'manufacturer_id'])
               ->prepare()
               ->execute([
                  'product_id' => $id,
                  'manufacturer_id' => $manufacturer_id
               ]);

            if ($manufacturer->affectedRows() <= 0) {
               throw new SystemException('Manufacturer relation not created', 400);
            }
         }
      }

      if (isset($dto->product_attr) && is_array($dto->product_attr)) {
         foreach ($dto->product_attr as $attr_id) {
            $attr = $this->database
               ->table('web_product_attr')
               ->insert(['product_id', 'attr_id'])
               ->prepare()
               ->execute([
                  'product_id' => $id,
                  'attr_id' => $attr_id
               ]);

            if ($attr->affectedRows() <= 0) {
               throw new SystemException('Attr relation not created', 400);
            }
         }
      }

      if (isset($dto->product_standard) && is_array($dto->product_standard)) {
         foreach ($dto->product_standard as $standard) {
            $standard_id = is_array($standard) ? $standard['standard_id'] : $standard;
            $value = is_array($standard) ? ($standard['value'] ?? null) : null;

            $standardResult = $this->database
               ->table('web_product_standard')
               ->insert(['product_id', 'standard_id', 'value'])
               ->prepare()
               ->execute([
                  'product_id' => $id,
                  'standard_id' => $standard_id,
                  'value' => $value
               ]);

            if ($standardResult->affectedRows() <= 0) {
               throw new SystemException('Standard relation not created', 400);
            }
         }
      }
   }

   private function updateRelation(ProductDTO $dto, array $tables): void {
      foreach ($tables as $table) {
         if (isset($dto->$table) && is_array($dto->$table)) {
            // Web tablolarından sil
            $this->database
               ->table('web_' . $table)
               ->delete()
               ->where(['product_id'])
               ->prepare()
               ->execute(['product_id' => $dto->id]);
         }
      }

      $this->createRelation($dto, $dto->id);
   }
}
