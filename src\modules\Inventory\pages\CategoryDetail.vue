<template>
   <Container v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ title }}</v-card-title>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               prepend-icon="$save"
               @click="form?.requestSubmit()">
               {{ t("app.save") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
            <LanguageTab
               v-model="language"
               v-bind:loading="isLoading" />
         </template>

         <v-card-text>
            <v-form
               ref="form"
               @submit.prevent="formHandler">
               <input
                  class="hidden"
                  type="submit" />
               <v-row no-gutters>
                  <v-col md="4">
                     <v-list-subheader>{{ t("app.code") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-text-field
                        v-model="category.code"
                        v-bind:rules="[appRules.required()]" />
                  </v-col>

                  <v-col md="4">
                     <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-text-field
                        v-model="category.title"
                        v-bind:rules="[appRules.required()]">
                        <template
                           v-if="language !== 1"
                           v-slot:append-inner>
                           <TranslateButton v-model="category.title" />
                        </template>
                     </v-text-field>
                  </v-col>

                  <v-col md="4">
                     <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-textarea
                        v-model="category.content"
                        v-bind:rules="[appRules.required()]"
                        auto-grow
                        no-resize>
                        <template
                           v-if="language !== 1"
                           v-slot:append-inner>
                           <TranslateButton v-model="category.content" />
                        </template>
                     </v-textarea>
                  </v-col>

                  <!-- <v-col md="4">
                     <v-list-subheader>{{ t("app.category") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <SelectInput
                        v-model="category.parent_id"
                        v-bind:items="categoryAll"
                        v-bind:loading="categoryLoading"
                        item-title="title"
                        item-value="id" />
                  </v-col> -->

                  <v-col md="4">
                     <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-switch
                        v-model="category.is_active"
                        v-bind:false-value="0"
                        v-bind:ripple="false"
                        v-bind:true-value="1"
                        color="primary"
                        density="compact">
                        <template v-slot:label>
                           <div class="text-sm">{{ category.is_active ? t("app.active") : t("app.passive") }}</div>
                        </template>
                     </v-switch>
                  </v-col>

                  <v-col md="4">
                     <v-list-subheader>{{ t("app.image") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <FileUpload
                        v-model="imageUpload"
                        v-bind:delete="deleteImageHandler"
                        v-bind:items="[category.image_path]" />
                  </v-col>
               </v-row>
            </v-form>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import FileUpload from "@/components/Input/FileUpload.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { useCreateCategory, useGetCategoryById, useUpdateCategory } from "../services/CategoryService";
import { useDeleteImage, useUploadImage } from "../services/ImageService";
import { ICategory, ICategoryStore } from "../utils/types";

const { t } = useI18n();
const route = useRoute() as IRoute;
const snackbar = useMessageStore();
const appStore = useAppStore();
const confirmStore = useConfirmStore();

// category
const form = ref<HTMLFormElement>();
const category = ref({
   is_active: 1
} as ICategory);
const routeId = computed(() => route.params.id);
const isCreate = computed(() => routeId.value === "create");
const isEnabled = computed(() => !!routeId.value && !isCreate.value);
const language = ref(1);
const title = computed(() => (isCreate.value ? t("inventory.createCategory") : t("inventory.categoryDetail")));
const imageUpload = ref([] as File[]);

// set breadcrumb
appStore.setBreadcrumb("CategoryDetail", title);

// services
const getCategoryById = useGetCategoryById({
   id: routeId,
   enabled: isEnabled,
   language: language,
   onSuccess: (data) => {
      category.value = { ...data };
   }
});
const updateCategory = useUpdateCategory();
const createCategory = useCreateCategory();
const uploadImage = useUploadImage();
const deleteImage = useDeleteImage({ invalidate: ["category", "categoryById"] });
// const { data: categoryData, isLoading: categoryLoading } = useGetCategoryAll();
// const categoryAll = computed(() => categoryData.value?.filter((item) => item.id !== category.value.id));

// loading
const isLoading = computed(() => getCategoryById.isLoading.value);
const isPending = computed(() => createCategory.isPending.value || updateCategory.isPending.value);

// handlers
const deleteCategoryImage = async () => {
   return await deleteImage.mutateAsync({
      id: category.value.id,
      path: category.value.image_path,
      table: "category"
   });
};

const uploadCategoryImage = async () => {
   return await uploadImage.mutateAsync({
      files: imageUpload.value,
      path: "category"
   });
};

const deleteImageHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirmTitle"),
         message: t("app.deleteImage")
      });

      if (confirm) {
         await deleteCategoryImage();
         snackbar.add({ text: t("app.imageDeleted") });
      }
   } catch {
      snackbar.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};

const formHandler = async () => {
   if (!form.value?.isValid) return;

   const payload: ICategoryStore = {
      code: category.value.code,
      // parent_id: category.value.parent_id,
      translate: [
         {
            language_id: language.value,
            title: category.value.title,
            content: category.value.content
         }
      ],
      is_active: category.value.is_active
   };

   try {
      if (imageUpload.value.length) {
         if (category.value.image_path) {
            await deleteCategoryImage();
            snackbar.add({ text: t("app.imageDeleted") });
         }

         const upload = await uploadCategoryImage();
         payload.image_path = upload.data[0];
         imageUpload.value = [];
      }

      if (isCreate.value) {
         const response = await createCategory.mutateAsync(payload);
         snackbar.add({ text: t("app.recordCreated") });
         router.push("/category/" + response.data.id);
      } else {
         await updateCategory.mutateAsync({ id: category.value.id, ...payload });
         snackbar.add({ text: t("app.recordUpdated") });
      }
   } catch (error) {
      snackbar.add({ text: t("app.recordFailed"), color: "error" });
   }
};
</script>
