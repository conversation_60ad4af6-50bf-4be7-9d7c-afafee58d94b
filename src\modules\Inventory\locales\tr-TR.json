{"inventory": {"management": "<PERSON><PERSON><PERSON>", "productList": "<PERSON><PERSON><PERSON><PERSON>", "productDetail": "<PERSON><PERSON><PERSON><PERSON>", "createProduct": "<PERSON><PERSON><PERSON><PERSON>", "updateProduct": "<PERSON><PERSON><PERSON><PERSON>", "deleteProduct": "<PERSON><PERSON><PERSON><PERSON>", "categoryList": "<PERSON><PERSON><PERSON>", "categoryDetail": "<PERSON><PERSON><PERSON>", "createCategory": "<PERSON><PERSON><PERSON>", "updateCategory": "<PERSON><PERSON><PERSON>", "deleteCategory": "<PERSON><PERSON><PERSON>", "manufacturerList": "<PERSON><PERSON>", "manufacturerDetail": "<PERSON><PERSON>", "createManufacturer": "<PERSON><PERSON>", "updateManufacturer": "<PERSON><PERSON>", "deleteManufacturer": "<PERSON><PERSON>", "attrList": "Özellik Listesi", "attrDetail": "Özellik Detayı", "createAttr": "Özellik Oluştur", "updateAttr": "Özellik Güncelle", "deleteAttr": "Özellik Sil", "standardList": "<PERSON><PERSON>", "standardDetail": "<PERSON><PERSON>", "createStandard": "<PERSON><PERSON>", "updateStandard": "<PERSON><PERSON>", "deleteStandard": "<PERSON><PERSON>", "competingList": "Rakip <PERSON>", "competingDetail": "Rakip Ürün <PERSON>", "createCompeting": "Rakip Ürü<PERSON>", "updateCompeting": "Rakip <PERSON>", "deleteCompeting": "Rakip Ü<PERSON>ü<PERSON>", "isCompeting": "<PERSON><PERSON><PERSON>"}}