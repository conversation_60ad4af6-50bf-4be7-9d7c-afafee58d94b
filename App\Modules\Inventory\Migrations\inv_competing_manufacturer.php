<?php

declare(strict_types=1);

use System\Migration\Migration;

class inv_competing_manufacturer extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `inv_competing_manufacturer` (
         `manufacturer_id` INT NOT NULL DEFAULT 0,
         `competing_id` INT NOT NULL DEFAULT 0,
         PRIMARY KEY (`manufacturer_id`, `competing_id`)
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `inv_competing_manufacturer`");
   }
}
