import { IStandard, IStandardStore } from "../utils/types";

export const useGetStandardAll = (payload?: IQuery<IStandard[]>) => {
   const options = computed(() => ({
      queryKey: ["standard", "standardAll"],
      queryFn: async () => {
         return (await appAxios.get("/inventory/standard/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetStandardById = (payload?: { id?: MaybeRef<string> } & IQuery<IStandard>) => {
   const options = computed(() => ({
      queryKey: ["standard", "standardById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/inventory/standard/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateStandard = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["standard", "updateStandard"],
      mutationFn: async (data: IStandardStore): Promise<IResponse<IStandard>> => {
         return (await appAxios.put("/inventory/standard/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["standard"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useCreateStandard = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["standard", "createStandard"],
      mutationFn: async (data: IStandardStore): Promise<IResponse<IStandard>> => {
         return (await appAxios.post("/inventory/standard/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["standard"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};
