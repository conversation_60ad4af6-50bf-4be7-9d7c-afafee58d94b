<?php

declare(strict_types=1);

namespace App\Modules\Website\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class CategoryRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'category'
   ) {
      $this->database->prefix('web_');
   }

   public function findAll(int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_category.*,
               COALESCE(web_category.is_active, inv_category.is_active) AS is_active,
               COALESCE(web_category.sort_order, inv_category.sort_order) AS sort_order,
               COALESCE(web_category.parent_id, inv_category.parent_id) AS parent_id,
               COALESCE(web_category_translate.title, inv_category_translate.title) AS title,
               COALESCE(web_category_translate.content, inv_category_translate.content) AS content,
               inv_category_translate.title AS origin_title,
               web_category_translate.url,
               web_category_translate.meta_title,
               web_category_translate.meta_description,
               web_category_translate.meta_keywords
            FROM inv_category

            LEFT JOIN web_category ON web_category.category_id = inv_category.id
               AND web_category.deleted_at IS NULL
            LEFT JOIN inv_category_translate ON inv_category_translate.category_id = inv_category.id
               AND inv_category_translate.language_id = :language_id_inv
            LEFT JOIN web_category_translate ON web_category_translate.category_id = inv_category.id
               AND web_category_translate.language_id = :language_id_web
            WHERE inv_category.deleted_at IS NULL
         ')
         ->execute([
            'language_id_inv' => $lang_id,
            'language_id_web' => $lang_id
         ])
         ->fetchAll();
   }

   public function findOne(int $id, int $lang_id): array|false {
      return $this->database
         ->prepare('SELECT
               inv_category.*,
               COALESCE(web_category.is_active, inv_category.is_active) AS is_active,
               COALESCE(web_category.sort_order, inv_category.sort_order) AS sort_order,
               COALESCE(web_category.parent_id, inv_category.parent_id) AS parent_id,
               COALESCE(web_category_translate.title, inv_category_translate.title) AS title,
               COALESCE(web_category_translate.content, inv_category_translate.content) AS content,
               inv_category_translate.title AS origin_title,
               web_category_translate.url,
               web_category_translate.meta_title,
               web_category_translate.meta_description,
               web_category_translate.meta_keywords
            FROM inv_category

            LEFT JOIN web_category ON web_category.category_id = inv_category.id
               AND web_category.deleted_at IS NULL
            LEFT JOIN inv_category_translate ON inv_category_translate.category_id = inv_category.id
               AND inv_category_translate.language_id = :language_id_inv
            LEFT JOIN inv_category_translate AS inv_default_translate ON inv_default_translate.category_id = inv_category.id
               AND inv_default_translate.language_id = 1
            LEFT JOIN web_category_translate ON web_category_translate.category_id = inv_category.id
               AND web_category_translate.language_id = :language_id_web
            LEFT JOIN web_category_translate AS web_default_translate ON web_default_translate.category_id = inv_category.id
               AND web_default_translate.language_id = 1
            WHERE inv_category.deleted_at IS NULL
               AND inv_category.id = :id
         ')
         ->execute([
            'id' => $id,
            'language_id_inv' => $lang_id,
            'language_id_web' => $lang_id
         ])
         ->fetch();
   }
}
