<?php

declare(strict_types=1);

use System\Migration\Migration;

class inv_attr_translate extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `inv_attr_translate` (
         `attr_id` INT NOT NULL DEFAULT 0,
         `language_id` INT NOT NULL DEFAULT 0,
         `title` VARCHAR(250) NOT NULL,
         PRIMARY KEY (`attr_id`, `language_id`)
      )");
      // FOREIGN KEY (`attr_id`) REFERENCES `attr`(`id`),
      // FOREIGN KEY (`language_id`) REFERENCES `language`(`id`)

      $this->database->table('inv_attr_translate')->insert([
         'attr_id' => 1,
         'language_id' => 1,
         'title' => 'Antistatik'
      ])->prepare()->execute();

      $this->database->table('inv_attr_translate')->insert([
         'attr_id' => 1,
         'language_id' => 2,
         'title' => 'Antistatic'
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `inv_attr_translate`");
   }
}
