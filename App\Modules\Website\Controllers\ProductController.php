<?php

declare(strict_types=1);

namespace App\Modules\Website\Controllers;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Website\DTOs\ProductDTO;
use App\Modules\Website\Services\ProductService;

/**
 * @OA\Tag(name="Product", description="Website ürün işlemleri")
 */
class ProductController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected ProductService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Product"}, path="/website/product/", summary="Website ürün listesi",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getAllProduct() {
      $this->response(function () {
         $result = $this->service->getAllProduct($this->language());
         return $result;
      });
   }

   /**
    * @OA\Get(
    *    tags={"Product"}, path="/website/product/{id}", summary="Website ürün detayı",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getProduct(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getProduct($id, $this->language());
         return $result;
      });
   }

   /**
    * @OA\Get(
    *    tags={"Product"}, path="/website/product/url/{url}", summary="URL ile ürün detayı",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="url", in="path", required=true, @OA\Schema(type="string")),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getProductByUrl(string $url) {
      $this->response(function () use ($url) {
         $result = $this->service->getProductByUrl($url, $this->language());
         return $result;
      });
   }

   /**
    * @OA\Get(
    *    tags={"Product"}, path="/website/product/category/{category_id}", summary="Kategoriye göre ürünler",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="category_id", in="path", required=true, @OA\Schema(type="integer")),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer")),
    *    @OA\Parameter(name="limit", in="query", required=false, @OA\Schema(type="integer", default=10)),
    *    @OA\Parameter(name="offset", in="query", required=false, @OA\Schema(type="integer", default=0))
    * )
    */
   public function getProductsByCategory(int $category_id) {
      $this->response(function () use ($category_id) {
         $limit = (int) ($this->request->get('limit') ?? 10);
         $offset = (int) ($this->request->get('offset') ?? 0);
         $result = $this->service->getProductsByCategory($category_id, $this->language(), $limit, $offset);
         return $result;
      });
   }

   /**
    * @OA\Get(
    *    tags={"Product"}, path="/website/product/search", summary="Ürün arama",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="q", in="query", required=true, @OA\Schema(type="string")),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer")),
    *    @OA\Parameter(name="limit", in="query", required=false, @OA\Schema(type="integer", default=10)),
    *    @OA\Parameter(name="offset", in="query", required=false, @OA\Schema(type="integer", default=0))
    * )
    */
   public function searchProducts() {
      $this->response(function () {
         $query = $this->request->get('q');
         if (empty($query)) {
            return [];
         }

         $limit = (int) ($this->request->get('limit') ?? 10);
         $offset = (int) ($this->request->get('offset') ?? 0);
         $result = $this->service->searchProducts($query, $this->language(), $limit, $offset);
         return $result;
      });
   }

   /**
    * @OA\Post(
    *    tags={"Product"}, path="/website/product/", summary="Website ürün oluştur",
    *    @OA\Response(response=201, description="Created"),
    *    @OA\RequestBody(@OA\JsonContent(
    *       @OA\Property(property="code", type="string", example="WEB001"),
    *       @OA\Property(property="is_active", type="integer", example=1),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="image_path", type="string", example="/uploads/product.jpg"),
    *       @OA\Property(property="translate", type="object",
    *          @OA\Property(property="language_id", type="integer", example=1),
    *          @OA\Property(property="title", type="string", example="Ürün Başlığı"),
    *          @OA\Property(property="content", type="string", example="Ürün açıklaması"),
    *          @OA\Property(property="url", type="string", example="urun-basligi"),
    *          @OA\Property(property="meta_title", type="string", example="Meta Başlık"),
    *          @OA\Property(property="meta_description", type="string", example="Meta açıklama"),
    *          @OA\Property(property="meta_keywords", type="string", example="anahtar, kelimeler")
    *       ),
    *       @OA\Property(property="product_category", type="array", @OA\Items(type="integer"), example={1, 2}),
    *       @OA\Property(property="product_manufacturer", type="array", @OA\Items(type="integer"), example={1}),
    *       @OA\Property(property="product_attr", type="array", @OA\Items(type="integer"), example={1, 2}),
    *       @OA\Property(property="product_standard", type="array", @OA\Items(type="object"), example={{"standard_id": 1, "value": "Değer"}}),
    *       @OA\Property(property="image_path_list", type="array", @OA\Items(type="string"), example={"/uploads/1.jpg", "/uploads/2.jpg"})
    *    ))
    * )
    */
   public function createProduct() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new ProductDTO();
         $dto->fromRequest($request);
         $result = $this->service->createProduct($dto, $this->language());
         return $result;
      }, code: 201);
   }

   /**
    * @OA\Put(
    *    tags={"Product"}, path="/website/product/", summary="Website ürün güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(@OA\JsonContent(
    *       @OA\Property(property="id", type="integer", example=1),
    *       @OA\Property(property="code", type="string", example="WEB001"),
    *       @OA\Property(property="is_active", type="integer", example=1),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="image_path", type="string", example="/uploads/product.jpg"),
    *       @OA\Property(property="translate", type="object",
    *          @OA\Property(property="language_id", type="integer", example=1),
    *          @OA\Property(property="title", type="string", example="Ürün Başlığı"),
    *          @OA\Property(property="content", type="string", example="Ürün açıklaması"),
    *          @OA\Property(property="url", type="string", example="urun-basligi"),
    *          @OA\Property(property="meta_title", type="string", example="Meta Başlık"),
    *          @OA\Property(property="meta_description", type="string", example="Meta açıklama"),
    *          @OA\Property(property="meta_keywords", type="string", example="anahtar, kelimeler")
    *       ),
    *       @OA\Property(property="product_category", type="array", @OA\Items(type="integer"), example={1, 2}),
    *       @OA\Property(property="product_manufacturer", type="array", @OA\Items(type="integer"), example={1}),
    *       @OA\Property(property="product_attr", type="array", @OA\Items(type="integer"), example={1, 2}),
    *       @OA\Property(property="product_standard", type="array", @OA\Items(type="object"), example={{"standard_id": 1, "value": "Değer"}}),
    *       @OA\Property(property="image_path_list", type="array", @OA\Items(type="string"), example={"/uploads/1.jpg", "/uploads/2.jpg"})
    *    ))
    * )
    */
   public function updateProduct() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new ProductDTO();
         $dto->fromRequest($request);
         $result = $this->service->updateProduct($dto, $this->language());
         return $result;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Product"}, path="/website/product/{id}", summary="Website ürün sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteProduct(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete(['id' => $id]);
         return $result;
      });
   }
}
