<?php

declare(strict_types=1);

namespace App\Core\Middlewares;

use System\Http\Request;
use System\Http\Response;

class Mikro {
   private $internal_key = 'ObyZ/eA5X03cNU0JnSJtKUlme+NvaotBaGMmqYTW1FE=';

   public function __construct(
      private Response $response,
      private Request $request
   ) {
   }

   public function handle(callable $next): mixed {
      if ($this->request->authorization() !== $this->internal_key) {
         return http_response_code(401);
      }

      header('Access-Control-Allow-Origin: *');
      header('Access-Control-Allow-Origin: http://localhost:5133');
      header('Access-Control-Allow-Credentials: true');
      header('Access-Control-Allow-Headers: Cache-Control, Pragma, Origin, Content-Type, Authorization, X-Requested-With');
      header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');

      return $next();
   }
}
