<template>
   <Container v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ t("inventory.competingList") }}</v-card-title>
            <ActionButton
               v-bind:disabled="isLoading"
               :to="$route.path + '/create'">
               {{ t("app.add") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <SearchInput v-model:search="filter" />
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data"
            v-bind:row-click="(item: ICompeting) => $router.push(`${$route.path}/${item.id}`)">
            <template v-slot:expand="{ item }">{{ item.details }}</template>
            <template v-slot:item.category_list="{ item }">{{ item.category_list.map((category: any) => category.title).join(", ") }}</template>
            <template v-slot:item.manufacturer_list="{ item }">{{ item.manufacturer_list.map((manufacturer: any) => manufacturer.title).join(", ") }}</template>
            <template v-slot:item.actions="{ item }">
               <div class="table-action text-right opacity-0 transition-opacity [tr:hover_.table-action]:!opacity-100">
                  <v-btn
                     v-ripple.stop
                     class="opacity-50 hover:!opacity-100"
                     icon="$edit"
                     @click="console.log(item)" />
                  <v-btn
                     v-ripple.stop
                     class="opacity-50 hover:!opacity-100"
                     icon="$accountProfile" />
               </div>
            </template>
         </DataTable>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { useGetCompetingAll } from "../services/CompetingService";
import { ICompeting } from "../utils/types";

const { t } = useI18n();

const filter = ref("");
const selected = ref([]);
const headers = computed((): IHeader<ICompeting>[] => [
   { title: t("app.title"), key: "title" },
   { title: t("app.category"), key: "category_list", width: "350" },
   { title: t("app.manufacturer"), key: "manufacturer_list", width: "150" }
]);

const { data, isLoading } = useGetCompetingAll();
</script>
