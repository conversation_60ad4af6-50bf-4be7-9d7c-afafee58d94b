# Windows
## Microsoft Store
**Minimum Gereksinim Ayarları ve <PERSON>**  
Örnek Intel Unison uygulaması için;  
- https://apps.microsoft.com/store/detail/intel%C2%AE-unison%E2%84%A2/9PP9GZM2GN26 adresinde yer alan son SLUG `9PP9GZM2GN26` hanesini kopyala.
- https://store.rg-adguard.net/ adresinden ProductId olarak arat ve sonuçlardan .msixbundle uzantılı olanı indir.
- İnen dosyayı 7zip ile ayıkla.
- Ayıklanan klasör içindeki .msix uzantılı dosyayı da 7zip ile ayıkla ve bu ayıklana klasöre gir.
- AppxManifest.xml dosyasınnı not defteri ile aç ve TargetDeviceFamily etiketinin MinVersion özelliğini değiştir.
- MinVersion="10.0.22621.0" gibi olan değeri MinVersion="10.0.19044.0" yap ve kaydet.
- AppxBlockMap.xml AppxSignature.p7x \[Content_Types].xml AppxMetadata folder dosya ve klasörlerini sil.
- Windows Geliştirici Modunu aç
- PowerShell ‘i yönetici olarak aç
- filepath = AppxManifest.xml dosyasına Shift ile sağ tıkla ve “Yol olarak kopyala”
- `Add-AppxPackage -Register filepath`

## Kopyalama Geçmişi
Tüm kopyalama geçmişini görüntülemek için Pano Ayarlarından Pano Geçmişi'ini açarak `Windows` + `V` tuşlarına basın.

## Server 2016 Lisanslama
```cmd
DISM /online /Get-TargetEditions
DISM /online /Set-Edition:ServerStandard /ProductKey:xxxx-xxxx /AcceptEula
```
## node_modules olmadan kopyalama
```cmd
robocopy SOURCE DEST /mir /xd node_modules
```

## Composer Path Fix
Bilgisayar ortam değişkenlerine `C:\ProgramData\ComposerSetup\bin` ekleyerek composer'ı global olarak kullanabilirsiniz.
> Bilgisayar > Sağ Tık > Özellikler > Gelişmiş Sistem Ayarları > Ortam Değişkenleri > Sistem Değişkenleri > Path > `C:\ProgramData\ComposerSetup\bin`

Sistem değişkenlerine ilave olarak `;.PHAR` eklenmelidir.