<template>
   <v-list-subheader v-if="props.item.itemType === 'subheader' && props.item.itemTitle" class="font-semibold pb-2 px-2">
      {{ t(props.item.itemTitle as string, 2) }}
   </v-list-subheader>

   <v-divider
      v-else-if="props.item.itemType === 'divider'"
      class="mb-[7px]" />

   <v-list-item
      v-else
      v-bind="props.item.itemProps">
      <template v-slot:append="{ isActive }">
         <v-icon
            v-if="props.item.children"
            v-bind:class="{ 'rotate-180': isActive }"
            class="transition duration-200"
            icon="$dropdown"
            size="x-small" />
      </template>

      <template v-slot:prepend></template>

      <v-list-item-title v-if="props.item.itemTitle">
         <div class="flex items-center">
            <v-icon
               v-if="props.item.itemProps?.prependIcon"
               v-bind:icon="props.item.itemProps?.prependIcon"
               class="me-2" />
            {{ t(props.item.itemTitle as string, 2) }}
         </div>
      </v-list-item-title>
   </v-list-item>
</template>

<script lang="ts" setup>
import type { TList } from "@/utils/types";

const { t } = useI18n();
const props = defineProps({
   item: {
      type: Object as () => TList,
      required: true
   }
});
</script>
