<?php

declare(strict_types=1);

use App\Core\Middlewares\Auth;
use App\Modules\Website\Controllers\ProductController;

/** @var System\Router\Router $router */

// Website Product routes - Admin Panel (Auth required)
$router->prefix('v1/website/product')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [ProductController::class, 'getAllProduct']);
   $router->post('/', [ProductController::class, 'createProduct']);
   $router->put('/', [ProductController::class, 'updateProduct']);
   $router->delete('/{id}', [ProductController::class, 'deleteProduct'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [ProductController::class, 'getProduct'])->where(['id' => '([0-9]+)']);
});

// Website Product routes - Public API (No auth required)
$router->prefix('v1/web/product')->group(function () use ($router) {
   // Tüm ürünler
   $router->get('/', [ProductController::class, 'getAllProduct']);
   
   // ID ile ürün detayı
   $router->get('/{id}', [ProductController::class, 'getProduct'])->where(['id' => '([0-9]+)']);
   
   // URL ile ürün detayı (SEO friendly)
   $router->get('/url/{url}', [ProductController::class, 'getProductByUrl'])->where(['url' => '([a-zA-Z0-9\-_]+)']);
   
   // Kategoriye göre ürünler
   $router->get('/category/{category_id}', [ProductController::class, 'getProductsByCategory'])->where(['category_id' => '([0-9]+)']);
   
   // Ürün arama
   $router->get('/search', [ProductController::class, 'searchProducts']);
});
