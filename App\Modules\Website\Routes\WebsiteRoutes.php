<?php

declare(strict_types=1);

use App\Core\Middlewares\Auth;
use App\Modules\Website\Controllers\ProductController;
use App\Modules\Website\Controllers\CategoryController;

/** @var System\Router\Router $router */

// Website Product routes - Admin Panel (Auth required)
$router->prefix('v1/website/product')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [ProductController::class, 'getAllProduct']);
   $router->post('/', [ProductController::class, 'createProduct']);
   $router->put('/', [ProductController::class, 'updateProduct']);
   $router->delete('/{id}', [ProductController::class, 'deleteProduct'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [ProductController::class, 'getProduct'])->where(['id' => '([0-9]+)']);
});

// Website Category routes - Admin Panel (Auth required)
$router->prefix('v1/website/category')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [CategoryController::class, 'getAllCategory']);
   $router->post('/', [CategoryController::class, 'createCategory']);
   $router->put('/', [CategoryController::class, 'overrideCategory']);
   $router->delete('/{id}', [CategoryController::class, 'deleteCategory'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [CategoryController::class, 'getCategory'])->where(['id' => '([0-9]+)']);
   $router->get('/tree', [CategoryController::class, 'getCategoryTree']);
   $router->get('/parent/{parent_id}', [CategoryController::class, 'getCategoriesByParent'])->where(['parent_id' => '([0-9]+)']);
});

// Website Product routes - Public API (No auth required)
$router->prefix('v1/web/product')->group(function () use ($router) {
   // Tüm ürünler
   $router->get('/', [ProductController::class, 'getAllProduct']);

   // ID ile ürün detayı
   $router->get('/{id}', [ProductController::class, 'getProduct'])->where(['id' => '([0-9]+)']);

   // URL ile ürün detayı (SEO friendly)
   $router->get('/url/{url}', [ProductController::class, 'getProductByUrl'])->where(['url' => '([a-zA-Z0-9\-_]+)']);

   // Kategoriye göre ürünler
   $router->get('/category/{category_id}', [ProductController::class, 'getProductsByCategory'])->where(['category_id' => '([0-9]+)']);

   // Ürün arama
   $router->get('/search', [ProductController::class, 'searchProducts']);
});

// Website Category routes - Public API (No auth required)
$router->prefix('v1/web/category')->group(function () use ($router) {
   // Tüm kategoriler
   $router->get('/', [CategoryController::class, 'getAllCategory']);

   // ID ile kategori detayı
   $router->get('/{id}', [CategoryController::class, 'getCategory'])->where(['id' => '([0-9]+)']);

   // URL ile kategori detayı (SEO friendly)
   $router->get('/url/{url}', [CategoryController::class, 'getCategoryByUrl'])->where(['url' => '([a-zA-Z0-9\-_]+)']);

   // Alt kategoriler
   $router->get('/parent/{parent_id}', [CategoryController::class, 'getCategoriesByParent'])->where(['parent_id' => '([0-9]+)']);

   // Kategori ağacı
   $router->get('/tree', [CategoryController::class, 'getCategoryTree']);
});
