<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Services;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Inventory\DTOs\AttrDTO;
use App\Modules\Inventory\Models\AttrModel;
use App\Modules\Inventory\Repositories\AttrRepository;

class AttrService extends BaseService {
   /** @var AttrRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      AttrRepository $repository
   ) {
      $this->repository = $repository;
   }

   public function getAllAttr(int $lang_id): array {
      $result = $this->repository->findAll($lang_id);
      return array_map(function ($item) {
         $attr = new AttrModel();
         $attr->fromRequest($item);

         return $attr;
      }, $result);
   }

   public function getAttr(int $id, int $lang_id): AttrModel {
      $result = $this->repository->findOne($id, $lang_id);
      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      $attr = new AttrModel();
      $attr->fromRequest($result);

      return $attr;
   }

   public function createAttr(AttrDTO $dto, int $lang_id): AttrModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
         ]);

         $id = $this->create([
            'sort_order' => $dto->sort_order
         ]);

         $this->translate($dto->translate, [
            'attr_id' => $id
         ], 'attr_translate');

         return $this->getAttr($id, $lang_id);
      });
   }

   public function updateAttr(AttrDTO $dto, int $lang_id): AttrModel | true {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'id' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
         ]);

         $this->update($dto, [
            'sort_order' => $dto->sort_order,
         ]);

         $this->translate($dto->translate, [
            'attr_id' => $dto->id,
         ], 'attr_translate');

         return $this->getAttr($dto->id, $lang_id);
      });
   }
}
