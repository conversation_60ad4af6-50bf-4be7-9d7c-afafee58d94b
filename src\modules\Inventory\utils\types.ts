export interface IProduct extends IDefaultFields {
   id: number;
   code: string;
   title: string;
   content: string;
   is_active: number;
   sort_order: number;
   category_list: { id: number; title: string }[];
   manufacturer_list: { id: number; title: string }[];
   standard_list: { id: number; title: string; value: string; image_path: string }[];
   attr_list: { id: number; title: string }[];
   image_list: { id: number; product_id: number; image_path: string }[];
}

export interface IProductStore {
   id?: number;
   code: string;
   is_active?: number;
   sort_order?: number;
   product_category?: number[];
   product_manufacturer?: number[];
   product_attr?: number[];
   product_standard?: {
      standard_id: number;
      value: string;
   }[];
   image_path?: string[];
   translate?: ITranslate[];
}

export interface ICompeting extends IDefaultFields {
   id: number;
   title: string;
   content: string;
   price: string;
   currency: string;
   image_path: string;
   category_list: { id: number; title: string }[];
   manufacturer_list: { id: number; title: string }[];
   standard_list: { id: number; title: string; value: string; image_path: string }[];
   attr_list: { id: number; title: string }[];
   product_list: { id: number; title: string; content: string; image_path: string; }[];
}

export interface ICompetingStore {
   id?: number;
   title: string;
   content: string;
   price: number;
   currency: string;
   image_path?: string;
   competing_product?: number[];
   competing_category?: number[];
   competing_manufacturer?: number[];
   competing_attr?: number[];
   competing_standard?: {
      standard_id: number;
      value: string;
   }[];
}

export interface ICategory extends IDefaultFields {
   id: number;
   code: string;
   title: string;
   content: string;
   image_path: string;
   is_active: number;
   sort_order: number;
   parent_id: number;
   group_id: number;
}

export interface ICategoryStore {
   id?: number;
   code: string;
   image_path?: string;
   is_active?: number;
   sort_order?: number;
   parent_id?: number;
   group_id?: number;
   translate?: ITranslate[];
   image_upload?: File[];
}

export interface IManufacturer extends IDefaultFields {
   id: number;
   code: string;
   title: string;
   content: string;
   image_path: string;
   is_active: number;
   is_competing: number;
   sort_order: number;
}

export interface IManufacturerStore {
   id?: number;
   code: string;
   image_path?: string;
   is_active?: number;
   is_competing?: number;
   sort_order?: number;
   translate?: ITranslate[];
}

export interface IStandard extends IDefaultFields {
   id: number;
   title: string;
   content: string;
   value: string;
   image_path: string;
   sort_order: number;
}

export interface IStandardStore {
   id?: number;
   image_path?: string;
   sort_order?: number;
   translate?: ITranslate[];
}

export interface IAttr extends IDefaultFields {
   id: number;
   title: string;
   sort_order: number;
}

export interface IAttrStore {
   id?: number;
   sort_order?: number;
   translate?: ITranslate[];
}
