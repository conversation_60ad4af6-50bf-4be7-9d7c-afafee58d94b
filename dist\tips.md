# Rehberler


## RAM Speed Öğrenme
```cmd
wmic memorychip get speed
```

## Diskpart

**Diskpart Format**
```cmd
list disk
select disk 1
clean
create partition primary
format fs=ntfs /q
assign
```

**Diskpart Delete Partition**
```cmd
list disk
select disk 1
list partition
select partition 2
delete partition
```

## Firewall
**Modem Bridge Mode**  
Modem Bridge Mode olduğunda operatör kullanıcı adı ve şifre PPPoE bilgileri güvenlik duvarına girilir.  
Gelen bağlantıları Güvenlik Duvarı karşılar. Port yönlendirmeleri daha düzgün yapılandırılır.  

Kablosuz Ayarları  
2.4GHz > Devre Dışı > Kaydet  
5GHz > Devre Dışı > Kaydet  
DHCP Sunucu > Devre Dışı > Kaydet  
Kolay Bridge/Köprü > İnternet Köprü Modu > Etkin (Sadece LAN1 Portu)

**Modem DMZ Mode**  
Modem DMZ Mode olduğunda operatör kullanıcı adı ve şifre PPPoE bilgileri modeme girilir ve DMZ ip si verilir.  
Gelen bağlantılar Modem tarafından karşılanır. `80 port` sorunu yaşanır.

**Kablolama**  
Modem LAN > Firewall WAN  
Firewall LAN > Local Network (Access Point, Switch)

**Kurulum**  
#### EXPERT MODE
Export mode enable

#### OBJECT
```
Address/Geo IP
Name        : SERVER_SUBNET
Adress Type : SUBNET
Network     : *************/24
Netmask     : *************

Address/Geo IP
Name        : PDKS_HOST
Adress Type : HOST
Ip Adress   : *************

Service
Name           : PDKS
IP Protocol    : TCP
Starting Port  : 4370

Service
Name           : HTTPS
IP Protocol    : TCP
Starting Port  : 4433

Service Group
PDKS_GROUP
+PDKS

Service Group
Default_Allow_WAN_To_ZyWALL
+HTTPS

ISP Account
WAN1_PPPoE_ACCOUNT
```

#### NETWORK
```
Interface > Ethernet > dmz > IP Adress Assigment
IP Adress   : ************

Interface > Ethernet > lan2 > IP Adress Assigment
IP Adress   : ************

Interface > Ethernet > lan1 > IP Adress Assigment
IP Adress   : 192.168.X.1

Interface > Ethernet > wan1 > IP Adress Assigment
Use Fixed IP Address
IP Adress   : 0.0.0.0
Subnet Mask : 0.0.0.0

GPON
Interface > VLAN > Interface Properties
Interface Type : external
Zone           : WAN
Base Port      : wan1
VLANID         : 35

GPON
Interface > VLAN > IP Adress Assigment
Use Fixed IP Address
IP Adress   : 0.0.0.0
Subnet Mask : 0.0.0.0

GPON
Interface > PPP > User Configuration
Interface Name : GPON
Base Interface : vlan35
Zone           : WAN

GPON
Interface > PPP > Connectivity
Nailed-UP

GPON
Interface > PPP > User Configuration
Account Profile   : WAN1_PPPoE_ACCOUNT
```


#### NAT
```
Rule Name            : PDKS
Incoming Interface   : wan1 OR GPON
Source IP            : any
External IP          : any
Internal IP          : PDKS_HOST
Port Mapping         : Service
External Service     : PDKS
Internal Service     : PDKS
```

#### SECURITY POLICY
```
Name        : PDKS
From        : WAN
To          : any (Excluding Zywall)
Source      : any
Destination : PDKS_HOST OR KEENETIC_HOST
Service     : PDKS_GROUP
User        : any
Schedule    : none
Action      : allow
Log         : no
```

#### SYSTEM
```
Hostname

WWW > 4433
```

#### LOG & REPORT
```
Log Settings > Remote Server 1
Log Format     : Syslog
Server Address : **************
Port           : 514
Network Traffic
Network DHCP
```

#### MAINTENANCE
Schedule Reboot
Weekly   : Sunday 3-0

## RDP WorkGroup Licensing
- Server Manager > Add Roles and Features
   - Remote Desktop Services
      - Remote Desktop Licensing
      - Remote Desktop Session Host
- Windows Administrative Tools
   - Remote Desktop Licensing Manager
   - Server > Properties > Connection Method > Web Browser
   - Server > Install Licenses (per device cal)
- Activate Microsoft
   - https://activate.microsoft.com
   - Install client access licenses
   - License Server ID  Windows Administrative Tools > Remote Desktop Licensing Manager > Server ID
   - License Program    Open License
   - Product Type       Windows Server 2019 Remote Desktop Services Per Device client access license
- Gpedit
   - Computer Configuration > Administrative Templates > Windows Components
   - Remote Desktop Services > Remote Desktop Session Host > Licensing
      - Use the specified Remote Desktop license server > License servers to use : *************** (rdp pc)
      - Set the Remote Desktop licesing mode > Per Device


License Server ID : BW6TJ-W7TBY-8VPB8-8XQKF-2HX3B-XWYVH-P286F  
Authorization Number : 04107656ZZS2210
License Number       : 74881820  
W4BTK-8RPRG-XW7Q2-CD4GX-BK8PG-XXC78-TGC23  

Authorization Number : 04379887ZZS2301  
License Number       : 75095254  
WFRC8-8M74C-F8XRM-33QYT-HRVKR-VCQVX-39XC3

## Modem PPPoE Şifresi Öğrenme
Gerekli Programlar;  
VMware Workstation ( https://www.vmware.com/products/workstation-pro.html )  
VMware Workstation hiçbir özel ayar yapılmadan “next, next” ile kur.  
Ubsol veya Linux Mint  

**Ubsol.vmx ile Kurulum;**  
Ubsol.vmx dosyası aç.  
VMware Workstation kurulumu ile birlikte gelen Virtual Network Editor uygulaması aç ve değiştirme işlemi için “Change Settings” tıkla.  
Bridge modunda bilgisayar Eternet bağdaştırıcısını seç ve kaydet.  
Sanal makineyi başlat “Power on this virtual machine” ve çıkan uyarıya “I Copied It” tıkla.  
Sanal makine başladıktan sonra oturum açmadan önce modem WAN dan bilgisayar Eternet’e gir.  

Terminal;  
Login    : test  
Password : 1234  

sudo -s  
1234  

(ikisini de dene)  
pppoe-server -F -I ens33 -O /etc/ppp/pppoe-server-options &  
pppoe-server -F -I ens32 -O /etc/ppp/pppoe-server-options &  

tail -f /var/log/pppoe-server-log  

**Linux Mint ile Kurulum;**  
Linux Mint ile VMware Workstation üzerine yeni sanal makine oluştur ve ayarlarından “Network Adapter” i “Bridge” moduna al.  
Sanal makineyi başlat `Power on this virtual machine`  

Terminal;  
(internete bağlı olması gerekiyor “Filesystem/etc/ppp” klasörüne yükleme yapacak)  
sudo su  
apt install pppoe  

1. `Filesystem/etc/ppp` klasörüne gir
2. Boşluk alana `Sağ Tık` > `Open as Root`
3. Yeni açılan `ppp` penceresinde `Create New Document`
4. `pppoe-server-options` isimli boş belge oluştur

pppoe-server-options içine kaydet;  
require-pap  
login  
lcp-echo-interval 10  
lcp-echo-failure 2  
show-password  
debug  
logfile /var/log/pppoe-server-log  

`ppp` klasörü içerisindeki `pap-secrets` belgesinin sonuna kaydet;  
\# Secrets for authentication using PAP  
\# client server secret IP addresses  
`"xxxx@fiber" * ""` (xxxx modem arayüzündeki pppoe kullanıcı adı)  

iwconfig  
pppoe-server -F -I eth0 -O /etc/ppp/pppoe-server-options (ethX değerini yaz)  
sudo tail -f /var/log/pppoe-server-log  