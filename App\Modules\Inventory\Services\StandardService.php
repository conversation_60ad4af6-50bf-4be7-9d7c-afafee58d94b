<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Services;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Inventory\DTOs\StandardDTO;
use App\Modules\Inventory\Models\StandardModel;
use App\Modules\Inventory\Repositories\StandardRepository;

class StandardService extends BaseService {
   /** @var StandardRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      StandardRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function getAllStandard(int $lang_id): array {
      $result = $this->repository->findAll($lang_id);
      return array_map(function ($item) {
         $standard = new StandardModel();
         $standard->fromRequest($item);

         return $standard;
      }, $result);
   }

   public function getStandard(int $id, int $lang_id): StandardModel {
      $result = $this->repository->findOne($id, $lang_id);
      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      $standard = new StandardModel();
      $standard->fromRequest($result);

      return $standard;
   }

   public function createStandard(StandardDTO $dto, int $lang_id): StandardModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'image_path' => 'required',
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required',
         ]);

         $id = $this->create([
            'image_path' => $dto->image_path,
            'sort_order' => $dto->sort_order,
         ]);

         $this->translate($dto->translate, [
            'standard_id' => $id
         ], 'standard_translate');

         return $this->getStandard($id, $lang_id);
      });
   }

   public function updateStandard(StandardDTO $dto, int $lang_id): StandardModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'id' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required',
         ]);

         $this->update($dto, [
            'image_path' => $dto->image_path,
            'sort_order' => $dto->sort_order,
         ]);

         $this->translate($dto->translate, [
            'standard_id' => $dto->id,
         ], 'standard_translate');

         return $this->getStandard($dto->id, $lang_id);
      });
   }
}
