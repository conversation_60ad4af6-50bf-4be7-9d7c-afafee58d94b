<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Services;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use App\Modules\Inventory\DTOs\StandardDTO;
use App\Modules\Inventory\Models\StandardModel;
use App\Modules\Inventory\Repositories\StandardRepository;

class StandardService extends BaseService {
   /** @var StandardRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      StandardRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function createStandard(StandardDTO $dto, int $lang_id): StandardModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'image_path' => 'required',
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required',
         ]);

         $id = $this->create([
            'image_path' => $dto->image_path,
            'sort_order' => $dto->sort_order,
         ]);

         $this->translate($dto->translate, [
            'standard_id' => $id
         ], 'standard_translate');

         return $this->getOne($id, $lang_id, StandardModel::class);
      });
   }

   public function updateStandard(StandardDTO $dto, int $lang_id): StandardModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'id' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required',
         ]);

         $this->update($dto, [
            'image_path' => $dto->image_path,
            'sort_order' => $dto->sort_order,
         ]);

         $this->translate($dto->translate, [
            'standard_id' => $dto->id,
         ], 'standard_translate');

         return $this->getOne($dto->id, $lang_id, StandardModel::class);
      });
   }
}
