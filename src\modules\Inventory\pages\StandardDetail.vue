<template>
   <Container v-bind:loading="isLoading" v-bind:error="isError">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ title }}</v-card-title>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               prepend-icon="$save"
               @click="form?.requestSubmit()">
               {{ t("app.save") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
            <LanguageTab
               v-model="language"
               v-bind:loading="isLoading" />
         </template>

         <v-card-text>
            <v-form
               ref="form"
               @submit.prevent="formHandler">
               <input
                  class="hidden"
                  type="submit" />
               <v-row no-gutters>
                  <v-col md="4">
                     <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-text-field
                        v-model="standard.title"
                        v-bind:rules="[appRules.required()]">
                        <template
                           v-if="language !== 1"
                           v-slot:append-inner>
                           <TranslateButton v-model="standard.title" />
                        </template>
                     </v-text-field>
                  </v-col>

                  <v-col md="4">
                     <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-textarea
                        v-model="standard.content"
                        v-bind:rules="[appRules.required()]"
                        auto-grow
                        no-resize>
                        <template
                           v-if="language !== 1"
                           v-slot:append-inner>
                           <TranslateButton v-model="standard.content" />
                        </template>
                     </v-textarea>
                  </v-col>

                  <v-col md="4">
                     <v-list-subheader>{{ t("app.image") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <FileUpload
                        v-model="imageUpload"
                        v-bind:delete="deleteImageHandler"
                        v-bind:items="[standard.image_path]" />
                  </v-col>
               </v-row>
            </v-form>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import FileUpload from "@/components/Input/FileUpload.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { useDeleteImage, useUploadImage } from "../services/ImageService";
import { useCreateStandard, useGetStandardById, useUpdateStandard } from "../services/StandardService";
import { IStandard, IStandardStore } from "../utils/types";

const { t } = useI18n();
const route = useRoute() as IRoute;
const snackbar = useMessageStore();
const appStore = useAppStore();
const confirmStore = useConfirmStore();

// standard
const form = ref<HTMLFormElement>();
const standard = ref({} as IStandard);
const routeId = computed(() => route.params.id);
const isCreate = computed(() => routeId.value === "create");
const isEnabled = computed(() => !!routeId.value && !isCreate.value);
const language = ref(1);
const title = computed(() => (isCreate.value ? t("inventory.createStandard") : t("inventory.standardDetail")));
const imageUpload = ref([] as File[]);

// set breadcrumb
appStore.setBreadcrumb("StandardDetail", title);

// services
const getStandardById = useGetStandardById({
   id: routeId,
   enabled: isEnabled,
   language: language,
   onSuccess: (data) => {
      standard.value = { ...data };
   }
});
const updateStandard = useUpdateStandard();
const createStandard = useCreateStandard();
const uploadImage = useUploadImage();
const deleteImage = useDeleteImage({ invalidate: ["standard", "standardById"] });

// loading
const isLoading = computed(() => getStandardById.isLoading.value);
const isPending = computed(() => createStandard.isPending.value || updateStandard.isPending.value);
const isError = computed(() => getStandardById.isError.value);

// handlers
const deleteStandardImage = async () => {
   return await deleteImage.mutateAsync({
      id: standard.value.id,
      path: standard.value.image_path,
      table: "standard"
   });
};

const uploadStandardImage = async () => {
   return await uploadImage.mutateAsync({
      files: imageUpload.value,
      path: "standard/"
   });
};

const deleteImageHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirmTitle"),
         message: t("app.deleteImage")
      });

      if (confirm) {
         await deleteStandardImage();
         snackbar.add({ text: t("app.imageDeleted") });
      }
   } catch {
      snackbar.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};

const formHandler = async () => {
   if (!form.value?.isValid) return;

   const payload: IStandardStore = {
      sort_order: 1,
      translate: [
         {
            language_id: language.value,
            title: standard.value.title,
            content: standard.value.content
         }
      ]
   };

   try {
      if (imageUpload.value.length) {
         if (standard.value.image_path) {
            await deleteStandardImage();
            snackbar.add({ text: t("app.imageDeleted") });
         }

         const upload = await uploadStandardImage();
         payload.image_path = upload.data[0];
         imageUpload.value = [];
      }

      if (isCreate.value) {
         await createStandard.mutateAsync(payload);
         snackbar.add({ text: t("app.recordCreated") });
      } else {
         await updateStandard.mutateAsync({ id: standard.value.id, ...payload });
         snackbar.add({ text: t("app.recordUpdated") });
      }
   } catch (error) {
      snackbar.add({ text: t("app.recordFailed"), color: "error" });
   }
};
</script>
