<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class AttrRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'attr'
   ) {
      $this->database->prefix('inv_');
   }

   public function findAll(int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_attr.*,
               inv_attr_translate.title
            FROM inv_attr

            LEFT JOIN inv_attr_translate ON inv_attr_translate.attr_id = inv_attr.id
               AND inv_attr_translate.language_id = :language_id
            WHERE inv_attr.deleted_at IS NULL
         ')
         ->execute([
            'language_id' => $lang_id
         ])
         ->fetchAll();
   }

   public function findOne(int $id, int $lang_id): array|false {
      return $this->database
         ->prepare('SELECT
               inv_attr.*,
               COALESCE(inv_attr_translate.title, default_translate.title) AS `title`
            FROM inv_attr

            LEFT JOIN inv_attr_translate ON inv_attr_translate.attr_id = inv_attr.id
               AND inv_attr_translate.language_id = :language_id
            LEFT JOIN inv_attr_translate AS default_translate ON default_translate.attr_id = inv_attr.id
               AND default_translate.language_id = 1
            WHERE inv_attr.deleted_at IS NULL
               AND inv_attr.id = :id
         ')
         ->execute([
            'id' => $id,
            'language_id' => $lang_id,
         ])
         ->fetch();
   }
}
