<template>
   <v-select
      v-model="model"
      v-bind="{ ...$attrs }"
      v-bind:item-title="props.itemTitle"
      v-bind:items="filterItems"
      v-bind:loading="props.loading"
      v-bind:menu-props="{ width: 300, maxHeight: 320 }"
      v-bind:multiple="props.multiple"
      v-bind:open-on-clear="props.openOnClear"
      transition="fade-transition"
      @click:clear="clearFilter">
      <template v-slot:prepend-item>
         <v-list-item
            v-if="props.search"
            class="mb-0 px-0">
            <v-list-item-title>
               <v-text-field
                  v-model="filterSearch"
                  hide-details
                  @click:clear="filterDebounce = ''"
                  @input="filterInput($event)"
                  @keydown="handleKeydown($event)"
                  @keydown.space.stop
                  @mousedown.stop>
                  <template v-slot:append-inner>
                     <v-icon>$search</v-icon>
                  </template>
               </v-text-field>
            </v-list-item-title>
         </v-list-item>

         <v-list-item
            v-if="props.multiple"
            class="mb-0"
            @click="selectAll(!allSelected)">
            <template
               v-if="props.multiple"
               v-slot:prepend>
               <v-checkbox-btn
                  v-bind:indeterminate="someSelected && !allSelected"
                  v-bind:model-value="allSelected"
                  v-bind:ripple="false"
                  class="w-10 text-base"
                  density="compact"
                  @update:model-value="selectAll(!allSelected)" />
            </template>
            <v-list-item-title>
               {{ t("app.selectAll") }}
            </v-list-item-title>
         </v-list-item>

         <v-divider class="my-2"></v-divider>
      </template>

      <template v-slot:item="{ item, props: itemProps }">
         <v-list-item v-bind="itemProps">
            <template
               v-if="props.multiple"
               v-slot:prepend="{ isSelected }">
               <v-checkbox-btn
                  density="compact"
                  v-bind:model-value="isSelected"
                  v-bind:ripple="false"
                  class="w-10 text-base" />
            </template>

            <template v-slot:title>
               <slot
                  v-bind:item="item"
                  name="item">
                  {{ item.title }}
               </slot>
            </template>
         </v-list-item>
      </template>

      <template v-slot:selection="{ item, index }">
         <template v-if="index === 0 && model.length > props.count && props.count > 0">
            <v-chip
               class="mr-1"
               color="primary"
               density="compact"
               size="small"
               variant="tonal">
               {{ model.length }}
            </v-chip>
         </template>

         <template v-if="index < props.count || props.count === 0">
            <span class="v-select__selection-text">
               <span>
                  {{ item.title }}
               </span>
               <template v-if="index < model.length - 1 && (index < props.count - 1 || props.count === 0)">
                  <span class="v-select__selection-comma">,</span>
               </template>
            </span>
         </template>
      </template>

      <template
         v-if="$slots.append"
         v-slot:append>
         <slot name="append" />
      </template>
   </v-select>
</template>

<script lang="ts" setup>
import type { TMultiSelect } from "@/utils/types";

type TProps = {
   items?: any[];
   count?: number;
   search?: boolean;
   multiple?: boolean;
   openOnClear?: boolean;
   menuProps?: any;
   loading?: boolean;
   itemTitle?: string;
};

const props = withDefaults(defineProps<TMultiSelect & TProps>(), {
   count: 2,
   search: true,
   multiple: false,
   openOnClear: true,
   loading: false,
   itemTitle: "title"
});

const model = defineModel({ type: [Array, Object, Number, null], default: null });
const { t } = useI18n();

const allSelected = computed(() => model.value && model.value.length === filterItems.value?.length);
const someSelected = computed(() => model.value && model.value.length > 0);

const selectAll = (value: boolean) => {
   model.value = value ? filterItems.value?.slice() : [];
};

const clearFilter = () => {
   filterDebounce.value = "";
   filterSearch.value = "";
};

const filterSearch = ref("");
const filterDebounce = ref("");
const filterInput = timerDebounce(async ($event) => {
   filterDebounce.value = $event.target.value;
});
const filterItems = computed(() => {
   return props.items?.filter((item: any) => {
      let value = lowerCase(filterDebounce.value.toString());
      // return !(value && !inputFilter(lowerCase(Object.values(item).join(" ").toString()), value));
      return !(value && !inputFilter(lowerCase(item[props.itemTitle].toString()), value));
   });
});

const handleKeydown = (event: KeyboardEvent) => event.key === "Escape" || event.stopPropagation();
</script>
