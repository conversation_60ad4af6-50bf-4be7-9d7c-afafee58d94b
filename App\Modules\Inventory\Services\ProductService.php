<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Services;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Inventory\DTOs\ProductDTO;
use App\Modules\Inventory\Models\ProductModel;
use App\Modules\Inventory\Repositories\ProductRepository;

class ProductService extends BaseService {
   /** @var ProductRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      ProductRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function getAllProduct(int $lang_id): array {
      $result = $this->repository->findAll($lang_id);
      return array_map(function ($item) use ($lang_id) {
         $product = new ProductModel();
         $product->fromRequest($item);

         $product->category_list = $this->repository->findCategory($product->id, 1);
         $product->manufacturer_list = $this->repository->findManufacturer($product->id, 1);
         $product->attr_list = $this->repository->findAttr($product->id, 1);
         $product->standard_list = $this->repository->findStandard($product->id, 1);
         $product->image_list = $this->repository->findImage($product->id);

         return $product;
      }, $result);
   }

   public function getProduct(int $id, int $lang_id): ProductModel {
      $result = $this->repository->findOne($id, $lang_id);
      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      $product = new ProductModel();
      $product->fromRequest($result);

      $product->category_list = $this->repository->findCategory($product->id, 1);
      $product->manufacturer_list = $this->repository->findManufacturer($product->id, 1);
      $product->attr_list = $this->repository->findAttr($product->id, 1);
      $product->standard_list = $this->repository->findStandard($product->id,1);
      $product->image_list = $this->repository->findImage($product->id);

      return $product;
   }

   public function createProduct(ProductDTO $dto, int $lang_id): ProductModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'code' => 'required',
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'product_category' => 'required|must_be_array',
            'product_manufacturer' => 'required|must_be_array',
            'product_attr' => 'required|must_be_array',
            'product_standard' => 'must_be_array',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required',
         ]);

         $id = $this->create([
            'code' => $dto->code,
            'is_active' => $dto->is_active,
            'sort_order' => $dto->sort_order,
         ]);

         $this->translate($dto->translate, [
            'product_id' => $id
         ], 'product_translate');

         $this->createRelation($dto, $id);

         return $this->getProduct($id, $lang_id);
      });
   }

   public function updateProduct(ProductDTO $dto, int $lang_id): ProductModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'id' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required',
         ]);

         $this->update($dto, [
            'code' => $dto->code,
            'is_active' => $dto->is_active,
            'sort_order' => $dto->sort_order,
         ]);

         if (isset($dto->image_path) && is_array($dto->image_path)) {
            foreach ($dto->image_path as $path) {
               $this->create([
                  'product_id' => $dto->id,
                  'image_path' => $path
               ], 'product_image');
            }
         }

         $this->translate($dto->translate, [
            'product_id' => $dto->id,
         ], 'product_translate');

         $this->updateRelation($dto, [
            'product_category',
            'product_manufacturer',
            'product_attr',
            'product_standard'
         ]);

         return $this->getProduct($dto->id, $lang_id);
      });
   }

   private function createRelation(ProductDTO $dto, int $id): void {
      if (isset($dto->product_category) && is_array($dto->product_category)) {
         foreach ($dto->product_category as $category_id) {
            $category = $this->repository->create([
               'product_id' => $id,
               'category_id' => $category_id
            ], 'product_category');

            if ($category->affectedRows() <= 0) {
               throw new SystemException('Category relation not created', 400);
            }
         }
      }

      if (isset($dto->product_manufacturer) && is_array($dto->product_manufacturer)) {
         foreach ($dto->product_manufacturer as $manufacturer_id) {
            $manufacturer = $this->repository->create([
               'product_id' => $id,
               'manufacturer_id' => $manufacturer_id
            ], 'product_manufacturer');

            if ($manufacturer->affectedRows() <= 0) {
               throw new SystemException('Manufacturer relation not created', 400);
            }
         }
      }

      if (isset($dto->product_attr) && is_array($dto->product_attr)) {
         foreach ($dto->product_attr as $attr_id) {
            $attr = $this->repository->create([
               'product_id' => $id,
               'attr_id' => $attr_id
            ], 'product_attr');

            if ($attr->affectedRows() <= 0) {
               throw new SystemException('Attr relation not created', 400);
            }
         }
      }

      if (isset($dto->product_standard) && is_array($dto->product_standard)) {
         foreach ($dto->product_standard as $standard) {
            $standard_id = is_array($standard) ? $standard['standard_id'] : $standard;
            $value = is_array($standard) ? ($standard['value'] ?? null) : null;

            $standard = $this->repository->create([
               'product_id' => $id,
               'standard_id' => $standard_id,
               'value' => $value
            ], 'product_standard');

            if ($standard->affectedRows() <= 0) {
               throw new SystemException('Standard relation not created', 400);
            }
         }
      }
   }

   private function updateRelation(ProductDTO $dto, array $tables): void {
      foreach ($tables as $table) {
         if (isset($dto->$table) && is_array($dto->$table)) {
            $this->repository->hardDelete([
               'product_id' => $dto->id
            ], $table);
         }
      }

      $this->createRelation($dto, $dto->id);
   }
}
