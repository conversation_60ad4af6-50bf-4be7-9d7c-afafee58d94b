<template>
   <Container v-bind:loading="isLoading" v-bind:form="formHandler">
         <Card v-bind:loading="isLoading || isPending">
            <template v-slot:header>
               <v-card-title class="flex-center p-0 text-base">{{ title }}</v-card-title>
               <ActionButton
                  v-bind:disabled="isLoading || isPending"
                  type="submit"
                  prepend-icon="$save">
                  {{ t("app.save") }}
               </ActionButton>
            </template>

            <template v-slot:extension>
               <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
               <LanguageTab
                  v-model="language"
                  v-bind:loading="isLoading" />
            </template>

            <v-card-text>
               <v-row no-gutters>
                  <v-col md="4">
                     <v-list-subheader>{{ t("app.code") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-text-field
                        v-model="product.code"
                        v-bind:rules="[appRules.required()]" />
                  </v-col>
                  <v-col md="4">
                     <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-text-field
                        v-model="product.title"
                        v-bind:rules="[appRules.required()]">
                        <template
                           v-if="language !== 1"
                           v-slot:append-inner>
                           <TranslateButton v-model="product.title" />
                        </template>
                     </v-text-field>
                  </v-col>
                  <v-col md="4">
                     <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-textarea
                        v-model="product.content"
                        v-bind:rules="[appRules.required()]"
                        auto-grow
                        no-resize>
                        <template
                           v-if="language !== 1"
                           v-slot:append-inner>
                           <TranslateButton v-model="product.content" />
                        </template>
                     </v-textarea>
                  </v-col>
               </v-row>
            </v-card-text>
         </Card>

         <Card>
            <template v-slot:extension>
               <v-card-title class="text-base">Bağlantılar</v-card-title>
            </template>

            <v-card-text>
               <v-row no-gutters>
                  <v-col md="4">
                     <v-list-subheader>{{ t("app.category") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <SelectInput
                        v-model="product.category_list"
                        v-bind:items="categoryAll"
                        v-bind:loading="categoryLoading"
                        v-bind:rules="[appRules.required()]"
                        item-value="id"
                        multiple
                        return-object />
                  </v-col>
                  <v-col md="4">
                     <v-list-subheader>{{ t("app.manufacturer") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <SelectInput
                        v-model="product.manufacturer_list"
                        v-bind:items="manufacturerAll"
                        v-bind:loading="manufacturerLoading"
                        v-bind:rules="[appRules.required()]"
                        item-value="id"
                        multiple
                        return-object />
                  </v-col>
                  <v-col md="4">
                     <v-list-subheader>{{ t("app.attribute") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <SelectInput
                        v-model="product.attr_list"
                        v-bind:items="attrAll"
                        v-bind:loading="attrLoading"
                        v-bind:rules="[appRules.required()]"
                        item-value="id"
                        multiple
                        return-object />
                  </v-col>
                  <v-col md="4">
                     <v-list-subheader>{{ t("app.active") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-switch
                        v-model="product.is_active"
                        v-bind:false-value="0"
                        v-bind:ripple="false"
                        v-bind:true-value="1"
                        color="primary"
                        density="compact">
                        <template v-slot:label>
                           <div class="text-sm">{{ product.is_active ? t("app.yes") : t("app.no") }}</div>
                        </template>
                     </v-switch>
                  </v-col>

                  <v-col md="4">
                     <v-list-subheader>{{ t("app.standard") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-row>
                        <v-col md="5">
                           <SelectInput
                              v-model="standardKey"
                              v-bind:items="standardAll"
                              v-bind:loading="standardLoading"
                              item-value="id"
                              return-object
                              @select="console.log($event)"
                              @update:model-value="selectHandler" />
                        </v-col>
                        <v-col md="7">
                           <v-text-field
                              v-model="standardVal"
                              v-bind:disabled="!standardKey"
                              ref="standardValElement"
                              @keydown.enter="addStandardHandler">
                              <template v-slot:append>
                                 <v-btn
                                    color="primary"
                                    density="default"
                                    variant="tonal"
                                    @click="addStandardHandler">
                                    {{ t("app.add") }}
                                 </v-btn>
                              </template>
                           </v-text-field>
                        </v-col>
                     </v-row>
                     <v-row no-gutters>
                        <v-col cols="12">
                           <DetailList
                              v-bind:data="product.standard_list"
                              v-bind:delete="deleteStandardHandler"
                              v-bind:to="'/inventory/standard/'"
                              title="title"
                              subtitle="value" />
                        </v-col>
                     </v-row>
                  </v-col>
                  <v-col md="4">
                     <v-list-subheader>{{ t("app.image", 2) }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <ImageList
                        v-bind:delete="deleteImageHandler"
                        v-bind:items="product.image_list" />
                     <ImageUpload
                        v-model="imageUpload"
                        multiple />
                  </v-col>
               </v-row>
            </v-card-text>
         </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import ImageList from "@/components/Input/ImageList.vue";
import ImageUpload from "@/components/Input/ImageUpload.vue";
import SelectInput from "@/components/Input/SelectInput.vue";
import DetailList from "@/components/List/DetailList.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { useGetAttrAll } from "../services/AttrService";
import { useGetCategoryAll } from "../services/CategoryService";
import { useDeleteImage, useUploadImage } from "../services/ImageService";
import { useGetManufacturerAll } from "../services/ManufacturerService";
import { useCreateProduct, useGetProductById, useUpdateProduct } from "../services/ProductService";
import { useGetStandardAll } from "../services/StandardService";
import { IProduct, IProductStore } from "../utils/types";

const { t } = useI18n();
const route = useRoute() as IRoute;
const snackbar = useMessageStore();
const appStore = useAppStore();
const confirmStore = useConfirmStore();

// product
const form = ref<HTMLFormElement>();
const standardValElement = ref<HTMLInputElement>();
const product = ref({
   is_active: 1,
   standard_list: [] as any,
   category_list: [] as any,
   manufacturer_list: [] as any,
   attr_list: [] as any
} as IProduct);
const routeId = computed(() => route.params.id);
const isCreate = computed(() => routeId.value === "create");
const isEnabled = computed(() => !!routeId.value && !isCreate.value);
const language = ref(1);
const title = computed(() => (isCreate.value ? t("inventory.createProduct") : t("inventory.productDetail")));
const imageUpload = ref([] as File[]);
const standardKey = ref();
const standardVal = ref();

// set breadcrumb
appStore.setBreadcrumb("ProductDetail", title);

// services
const getProductById = useGetProductById({
   id: routeId,
   enabled: isEnabled,
   language: language,
   onSuccess: (item) => {
      product.value = { ...item, standard_list: [...item.standard_list] };
   }
});
const updateProduct = useUpdateProduct();
const createProduct = useCreateProduct();
const uploadImage = useUploadImage();
const deleteImage = useDeleteImage({ invalidate: ["product", "productById"] });

// relation services
const { data: categoryAll, isLoading: categoryLoading } = useGetCategoryAll();
const { data: manufacturerAll, isLoading: manufacturerLoading } = useGetManufacturerAll({ params: { competing: 0 } });
const { data: attrAll, isLoading: attrLoading } = useGetAttrAll();
const { data: standardData, isLoading: standardLoading } = useGetStandardAll();
const standardAll = computed(() => standardData.value?.filter((item) => !product.value.standard_list?.find((i) => i.id === item.id)));

// loading
const isLoading = computed(() => getProductById.isLoading.value);
const isPending = computed(() => createProduct.isPending.value || updateProduct.isPending.value);

// handlers
const selectHandler = () => {
   if (!standardKey.value) {
      standardVal.value = null;
   } else {
      setTimeout(() => {
         standardValElement.value?.focus();
      }, 50);
   }
};

const addStandardHandler = () => {
   product.value.standard_list.push({
      ...standardKey.value,
      value: standardVal.value
   });
   standardKey.value = null;
   standardVal.value = null;
};

const deleteStandardHandler = (id: number) => {
   product.value.standard_list = product.value.standard_list.filter((item) => item.id !== id);
};

const deleteImageHandler = async (image: any) => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirmTitle"),
         message: t("app.deleteImage")
      });

      if (confirm) {
         await deleteImage.mutateAsync({
            id: image.id,
            path: image.image_path,
            table: "product_image",
            method: "delete",
            unlink: false
         });
         snackbar.add({ text: t("app.imageDeleted") });
      }
   } catch {
      snackbar.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};

const formHandler = async () => {
   // if (!form.value?.isValid) return;

   const payload: IProductStore = {
      code: product.value.code,
      translate: [
         {
            language_id: language.value,
            title: product.value.title,
            content: product.value.content
         }
      ],
      product_category: product.value.category_list.map((item) => item.id),
      product_manufacturer: product.value.manufacturer_list.map((item) => item.id),
      product_attr: product.value.attr_list.map((item) => item.id),
      product_standard: product.value.standard_list.map((item) => ({
         standard_id: item.id,
         value: item.value
      })),
      is_active: product.value.is_active,
      sort_order: product.value.sort_order
   };

   try {
      if (imageUpload.value.length) {
         const upload = await uploadImage.mutateAsync({
            files: imageUpload.value,
            path: "product/" + product.value.id
         });
         payload.image_path = upload.data;

         imageUpload.value = [];
      }

      if (isCreate.value) {
         const response = await createProduct.mutateAsync(payload);
         snackbar.add({ text: t("app.recordCreated") });
         router.push("/product/" + response.data.id);
      } else {
         await updateProduct.mutateAsync({ id: product.value.id, ...payload });
         snackbar.add({ text: t("app.recordUpdated") });
      }
   } catch (error) {
      snackbar.add({ text: t("app.recordFailed"), color: "error" });
   }
};
</script>
