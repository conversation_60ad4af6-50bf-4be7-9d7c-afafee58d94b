<?php

declare(strict_types=1);

use System\Migration\Migration;

class web_product extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `web_product` (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `code` VARCHAR(150) NOT NULL,
         `is_active` BOOLEAN NOT NULL DEFAULT 1,
         `sort_order` INT NOT NULL DEFAULT 0,
         {$this->defaults()}
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `web_product`");
   }
}
