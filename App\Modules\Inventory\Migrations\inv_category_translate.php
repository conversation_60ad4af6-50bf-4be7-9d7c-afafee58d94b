<?php

declare(strict_types=1);

use System\Migration\Migration;

class inv_category_translate extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `inv_category_translate` (
         `category_id` INT NOT NULL DEFAULT 0,
         `language_id` INT NOT NULL DEFAULT 0,
         `title` VARCHAR(250) NOT NULL,
         `content` TEXT NULL DEFAULT NULL,
         {$this->defaults()},
         PRIMARY KEY (`category_id`, `language_id`)
      )");
      // FOREIGN KEY (`category_id`) REFERENCES `category`(`id`),
      // FOREIGN KEY (`language_id`) REFERENCES `language`(`id`)

      $this->database->table('inv_category_translate')->insert([
         'category_id' => 1,
         'language_id' => 1,
         'title' => 'Eldivenler'
      ])->prepare()->execute();

      $this->database->table('inv_category_translate')->insert([
         'category_id' => 1,
         'language_id' => 2,
         'title' => 'Gloves'
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `inv_category_translate`");
   }
}
