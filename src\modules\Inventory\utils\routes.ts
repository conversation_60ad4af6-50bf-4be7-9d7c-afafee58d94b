import DefaultLayout from "@/components/Layout/Default/Layout.vue";

export const inventoryRoutes: RouteRecordRaw[] = [
   {
      path: "/inventory/product",
      meta: {
         title: "app.product",
         breadcrumb: "inventory.productList",
         layout: DefaultLayout
      },
      children: [
         {
            path: "",
            component: getComponent(() => import("../pages/ProductList.vue")),
         },
         {
            path: ":id(create|[0-9]+)",
            component: getComponent(() => import("../pages/ProductDetail.vue")),
            meta: {
               breadcrumb: "{ProductDetail}"
            }
         }
      ]
   },
   {
      path: "/inventory/category",
      meta: {
         title: "app.category",
         breadcrumb: "inventory.categoryList",
         layout: DefaultLayout
      },
      children: [
         {
            path: "",
            component: getComponent(() => import("../pages/CategoryList.vue")),
         },
         {
            path: ":id(create|[0-9]+)",
            component: getComponent(() => import("../pages/CategoryDetail.vue")),
            meta: {
               breadcrumb: "{CategoryDetail}"
            }
         }
      ]
   },
   {
      path: "/inventory/manufacturer",
      meta: {
         title: "app.manufacturer",
         breadcrumb: "inventory.manufacturerList",
         layout: DefaultLayout
      },
      children: [
         {
            path: "",
            component: getComponent(() => import("../pages/ManufacturerList.vue")),
         },
         {
            path: ":id(create|[0-9]+)",
            component: getComponent(() => import("../pages/ManufacturerDetail.vue")),
            meta: {
               breadcrumb: "{ManufacturerDetail}"
            }
         }
      ]
   },
   {
      path: "/inventory/standard",
      meta: {
         title: "app.standard",
         breadcrumb: "inventory.standardList",
         layout: DefaultLayout
      },
      children: [
         {
            path: "",
            component: getComponent(() => import("../pages/StandardList.vue")),
         },
         {
            path: ":id(create|[0-9]+)",
            component: getComponent(() => import("../pages/StandardDetail.vue")),
            meta: {
               breadcrumb: "{StandardDetail}"
            }
         }
      ]
   },
   {
      path: "/inventory/attribute",
      meta: {
         title: "app.attribute",
         breadcrumb: "inventory.attrList",
         layout: DefaultLayout
      },
      children: [
         {
            path: "",
            component: getComponent(() => import("../pages/AttrList.vue")),
         },
         {
            path: ":id(create|[0-9]+)",
            component: getComponent(() => import("../pages/AttrDetail.vue")),
            meta: {
               breadcrumb: "{AttrDetail}"
            }
         }
      ]
   },
   {
      path: "/inventory/competing",
      meta: {
         title: "app.competing",
         breadcrumb: "inventory.competingList",
         layout: DefaultLayout
      },
      children: [
         {
            path: "",
            component: getComponent(() => import("../pages/CompetingList.vue")),
         },
         {
            path: ":id(create|[0-9]+)",
            component: getComponent(() => import("../pages/CompetingDetail.vue")),
            meta: {
               breadcrumb: "{CompetingDetail}"
            }
         }
      ]
   }
];
