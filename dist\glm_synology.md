# Synology NAS
## Genel Bilgi
Synology NAS DS218 2 yuvalı modelini kullanıyoruz.  
`1TB` + `1TB` 2 adet sunucu HDD si mevcut ve `RAID 1` konfigürasyonu uygulandı.  
Cihazın IP adresi bilinmiyor ve bağlanılamıyorsa https://finds.synology.com adresinden yararlanabilir veya [Synology Assistant](https://www.synology.com/en-nz/support/download) masaüstü programını kullanılabilir.

## IP Yapılandırması
*Denetim Masası > Ağ > Ağ Arabirimi > LAN (Düzenle)*  
IP `***************`  
DNS `*******`  

## Port Yapılandırması
*Denetim Masası > Oturum Açma Portalı*  
HTTP `6699`  
HTTPS `9898`  

## Erişim
Network Access `***************:6699`  
External Access `STATICIP:6699`  
External Access DDNS `gulemticaret.synology.me:6699`  
External Access Domain A Record `nas.gulemticaret.com:6699`  

## Güvenlik
*Denetim Masası > Ağ > Bağlantı*  
HTTP/2 Etkin

*Denetim Masası > Ağ > Güvenlik > Hesap*  
2FA Çift faktörlü kimlik doğrulama `BITWARDEN`

## Paylaştırılmış Klasör
4 adet paylaştırılmış klasör mevcut.
- Depo
- Paylaşım
- Programlar
- Vardabit

Bunlardan sadece Paylaşım görüntülebilir diğerleri gizli durumda.  
- *"Ağ Bağlantılarım" içinde bu paylaşımlı klasörü gizleyin*
- *İzinlere sahip olmayan kullanıcılardan alt klasörleri ve dosyaları gizle*

Yeni bir Paylaştırılmış Klasör oluştururken izinleri;
- administrator grubu `Oku/Yaz`
- users grubu `Salt Okunur`

## Kullanıcılar
Her çalışan için ayrı kullanıcı kısa kodu ile tanımla. Açıklama kısmına çalışanın adını ve soyadını yaz.  
Güçlü bir parola oluştur. Parolayı kullanıcı ile `paylaşma` ve *Kullanıcının hesap parolasını değiştirmesine izin verme* seçeneğini aktif hale getir.  
Kullanıcıyı ilgili gruba ata.

## Yedekleme
Synology üzerindeki `Hyper Backup` ile gulemticaret\@gmail.com Google Drive hesabımıza her gün gece saat `02:00` da yedek alınıyor. Yedek döngüsü 14 gün.  
Yedeklemenin tamamlanma bilgisi cmd\@gulemticaret.com adresinden bilgi\@gulemticaret.com mail ile bildiriliyor.  
*Denetim Masası > Bildirim > Kurallar > Custom*


