<template>
   <v-fade-transition leave-absolute>
      <LayoutLoader v-if="loading" />

      <component
         v-else
         v-bind:is="$route.meta.layout" />
   </v-fade-transition>

   <VueQueryDevtools />
</template>

<script lang="ts" setup>
import LayoutLoader from "@/components/Loader/LayoutLoader.vue";
import { VueQueryDevtools } from "@tanstack/vue-query-devtools";

const appStore = useAppStore();
const loading = computed(() => appStore.layoutLoading || appStore.menuLoading || appStore.i18nLoading);

onMounted(() => {
   if (appConfig.default.ripple === false) {
      document.documentElement.classList.add("v-ripple--false");
   }
});
</script>
