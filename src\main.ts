// #1
import "@/assets/style/main.scss";
// #2
import App from "@/App.vue";
import { registerMenu } from "@/utils/menu";
import { registerI18n } from "@/utils/i18n";

const app = createApp(App);

app.use(pinia);
app.use(i18n);
app.use(vuetify);
app.use(query, queryOptions);
registerRoutes().then((routes) => {
   app.use(routes);
   app.mount("#app");
});
registerI18n();
registerMenu();
registerProviders(app);
registerDirectives(app);

// app.mixin({
//    created() {
//       console.log("[created] " + this.$options.name);
//    }
// });
