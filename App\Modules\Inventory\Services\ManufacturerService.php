<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Services;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Inventory\DTOs\ManufacturerDTO;
use App\Modules\Inventory\Models\ManufacturerModel;
use App\Modules\Inventory\Repositories\ManufacturerRepository;

class ManufacturerService extends BaseService {
   /** @var ManufacturerRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      ManufacturerRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function getAllManufacturer(int $lang_id, ?array $request = null): array {
      $where = [];
      if (!is_null($request)) {
         if (isset($request['competing'])) {
            $where['is_competing'] = $request['competing'];
         }
      }

      $result = $this->repository->findAll($lang_id, $where);
      return array_map(function ($item) {
         $manufacturer = new ManufacturerModel();
         $manufacturer->fromRequest($item);

         return $manufacturer;
      }, $result);
   }

   public function getManufacturer(int $id, int $lang_id): ManufacturerModel {
      $result = $this->repository->findOne($id, $lang_id);
      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      $manufacturer = new ManufacturerModel();
      $manufacturer->fromRequest($result);

      return $manufacturer;
   }

   public function createManufacturer(ManufacturerDTO $dto, int $lang_id): ManufacturerModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'code' => 'required',
            'image_path' => 'nullable',
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
         ]);

         $id = $this->create([
            'code' => $dto->code,
            'image_path' => $dto->image_path,
            'is_active' => $dto->is_active,
            'sort_order' => $dto->sort_order,
         ]);

         $this->translate($dto->translate, [
            'manufacturer_id' => $id
         ], 'manufacturer_translate');

         return $this->getManufacturer($id, $lang_id);
      });
   }

   public function updateManufacturer(ManufacturerDTO $dto, int $lang_id): ManufacturerModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'id' => 'required|numeric',
            'translate' => 'required|must_be_array',
         ]);

         $this->validate($dto->translate, [
            'language_id' => 'required|numeric',
            'title' => 'required',
         ]);

         $this->update($dto, [
            'code' => $dto->code,
            'image_path' => $dto->image_path,
            'is_active' => $dto->is_active,
            'is_competing' => $dto->is_competing,
            'sort_order' => $dto->sort_order,
         ]);

         $this->translate($dto->translate, [
            'manufacturer_id' => $dto->id,
         ], 'manufacturer_translate');

         return $this->getManufacturer($dto->id, $lang_id);
      });
   }
}
