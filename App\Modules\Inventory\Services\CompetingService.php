<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Services;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Inventory\DTOs\CompetingDTO;
use App\Modules\Inventory\Models\CompetingModel;
use App\Modules\Inventory\Repositories\ProductRepository;
use App\Modules\Inventory\Repositories\CompetingRepository;

class CompetingService extends BaseService {
   /** @var CompetingRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      protected ProductRepository $productRepository,
      CompetingRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function getAllCompeting(int $lang_id): array {
      $result = $this->repository->findAll($lang_id);
      return array_map(function ($item) use ($lang_id) {
         $competing = new CompetingModel();
         $competing->fromRequest($item);

         $competing->category_list = $this->repository->findCategory($competing->id, 1);
         $competing->manufacturer_list = $this->repository->findManufacturer($competing->id, 1);
         $competing->attr_list = $this->repository->findAttr($competing->id, 1);
         $competing->standard_list = $this->repository->findStandard($competing->id, 1);
          $competing->product_list = array_map(function ($product) {
            $product['image_list'] = $this->productRepository->findImage($product['id']);
            return $product;
         }, $this->repository->findProduct($competing->id, 1));

         return $competing;
      }, $result);
   }

   public function getCompeting(int $id, int $lang_id): CompetingModel {
      $result = $this->repository->findOne($id, $lang_id);
      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      $competing = new CompetingModel();
      $competing->fromRequest($result);

      $competing->category_list = $this->repository->findCategory($competing->id, 1);
      $competing->manufacturer_list = $this->repository->findManufacturer($competing->id, 1);
      $competing->attr_list = $this->repository->findAttr($competing->id, 1);
      $competing->standard_list = $this->repository->findStandard($competing->id, 1);
      $competing->product_list = array_map(function ($product) {
         $product['image_list'] = $this->productRepository->findImage($product['id']);
         return $product;
      }, $this->repository->findProduct($competing->id, 1));

      return $competing;
   }

   public function createCompeting(CompetingDTO $dto, int $lang_id): CompetingModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->validate($dto->toArray(), [
            'title' => 'required',
            'content' => 'required',
            'price' => 'numeric',
         ]);

         $id = $this->create([
            'title' => $dto->title,
            'content' => $dto->content,
            'price' => $dto->price,
            'currency' => $dto->currency,
            'image_path' => $dto->image_path
         ]);

         $this->createRelation($dto, $id);

         return $this->getCompeting($id, $lang_id);
      });
   }

   public function updateCompeting(CompetingDTO $dto, int $lang_id): CompetingModel {
      return $this->transaction(function () use ($dto, $lang_id) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'id' => 'required|numeric',
            'title' => 'required',
            'content' => 'required',
            'price' => 'numeric',
         ]);

         $this->update($dto, [
            'title' => $dto->title,
            'content' => $dto->content,
            'price' => $dto->price,
            'currency' => $dto->currency,
            'image_path' => $dto->image_path
         ]);

         $this->updateRelation($dto, [
            'competing_category',
            'competing_manufacturer',
            'competing_attr',
            'competing_standard',
            'competing_product'
         ]);

         return $this->getCompeting($dto->id, $lang_id);
      });
   }

   private function createRelation(CompetingDTO $dto, int $id): void {
      if (isset($dto->competing_product) && is_array($dto->competing_product)) {
         foreach ($dto->competing_product as $product_id) {
            $product = $this->repository->create([
               'competing_id' => $id,
               'product_id' => $product_id
            ], 'competing_product');

            if ($product->affectedRows() <= 0) {
               throw new SystemException('Product relation not created', 400);
            }
         }
      }

      if (isset($dto->competing_category) && is_array($dto->competing_category)) {
         foreach ($dto->competing_category as $category_id) {
            $category = $this->repository->create([
               'competing_id' => $id,
               'category_id' => $category_id
            ], 'competing_category');

            if ($category->affectedRows() <= 0) {
               throw new SystemException('Category relation not created', 400);
            }
         }
      }

      if (isset($dto->competing_manufacturer) && is_array($dto->competing_manufacturer)) {
         foreach ($dto->competing_manufacturer as $manufacturer_id) {
            $manufacturer = $this->repository->create([
               'competing_id' => $id,
               'manufacturer_id' => $manufacturer_id
            ], 'competing_manufacturer');

            if ($manufacturer->affectedRows() <= 0) {
               throw new SystemException('Manufacturer relation not created', 400);
            }
         }
      }

      if (isset($dto->competing_attr) && is_array($dto->competing_attr)) {
         foreach ($dto->competing_attr as $attr_id) {
            $attr = $this->repository->create([
               'competing_id' => $id,
               'attr_id' => $attr_id
            ], 'competing_attr');

            if ($attr->affectedRows() <= 0) {
               throw new SystemException('Attribute relation not created', 400);
            }
         }
      }

      if (isset($dto->competing_standard) && is_array($dto->competing_standard)) {
         foreach ($dto->competing_standard as $standard) {
            $standard_id = is_array($standard) ? $standard['standard_id'] : $standard;
            $value = is_array($standard) ? ($standard['value'] ?? null) : null;

            $standard = $this->repository->create([
               'competing_id' => $id,
               'standard_id' => $standard_id,
               'value' => $value
            ], 'competing_standard');

            if ($standard->affectedRows() <= 0) {
               throw new SystemException('Standard relation not created', 400);
            }
         }
      }
   }

   private function updateRelation(CompetingDTO $dto, array $tables): void {
      foreach ($tables as $table) {
         if (isset($dto->$table) && is_array($dto->$table)) {
            $this->repository->hardDelete([
               'competing_id' => $dto->id
            ], $table);
         }
      }

      $this->createRelation($dto, $dto->id);
   }
}
