# Mikro ERP

## EXE Güncelleme

1. <PERSON><PERSON><PERSON> kullanıcılara çıkış yaptır
2. C<PERSON> ve Server yönetici olarak çalıştır (özel ayar yok ileri ileri)
3. <PERSON><PERSON><PERSON>dan sonra 504000 ve 504001 tablo sürüm yükselt
4. <PERSON><PERSON><PERSON> ka<PERSON>t aç
5. dB <PERSON><PERSON><PERSON>elerini Düzelt
   -  SIPARISLER_CHOOSE_40
   -  STOKLAR_YONETIM

## Barkod Oluşturma (Eski Yöntem)

!> Artık barkod oluşturmuyoruz.

**019577 - Varyantsız Barkod Oluşturma**

Varyantı olmayan stok kartlarına barkod ekler, varyantlı olanlarda ürünün kendisine barkod ekler.  
Başlangıç : `2`  
Uzunluk : `8`  
Barkod Tipi : `Ean-13`  
Başlangıç Kodu : `1531` _sona 1 ekle_

**019554 - Varyantlı Barkod Oluşturma**  
Sadece varyantlı ürünlerin varyantlarına barkod ekler.  
Barkod Tipi : `Ean-13`  
Başlang<PERSON>ç Kodu : `153`

## Özet Tabloların Güncellenmesi

Özet raporlar ile hareket kayıtlarındaki değerler (tutarlar) tutmadığında güncelleme yapılmalıdır. `505046`

## Cari Personel Aktarılması

```sql
UPDATE STOK_HAREKETLERI SET sth_plasiyer_kodu='1001' where sth_plasiyer_kodu='1003'
UPDATE CARI_HESAP_HAREKETLERI SET cha_satici_kodu='1001' where cha_satici_kodu='1003'
UPDATE SIPARISLER SET sip_satici_kod='1001' where sip_satici_kod='1003'
UPDATE PROFORMA_SIPARISLER SET pro_saticikodu='1001' where pro_saticikodu='1003'
UPDATE SATINALMA_TALEPLERI SET stl_talep_eden='1001' where stl_talep_eden='1003'
UPDATE SIPARISLER SET sip_create_user='1001' where sip_create_user='1003'
UPDATE CARI_HESAPLAR SET cari_create_user='1001' where cari_create_user='1003'
UPDATE CARI_HESAPLAR SET cari_temsilci_kodu='1001' where cari_temsilci_kodu='1003'
```

## GYS CRM Teklif Açığa Çekme ve Kapatma

Teklif GUID:  
<input type="text" oninput="document.getElementById('OffersOn').innerHTML = this.value; document.getElementById('OffersOff').innerHTML = this.value"  placeholder="00000000-0000-0000-0000-000000000000"/>

### Açma

UPDATE Offers SET StatusType = 100, IsSuccess = 0  
WHERE Id = '<span id="OffersOn">00000000-0000-0000-0000-000000000000</span>'

### Kapatma

UPDATE Offers SET StatusType = 101, IsSuccess = 1  
WHERE Id = '<span id="OffersOff">00000000-0000-0000-0000-000000000000</span>'
