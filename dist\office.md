# Microsoft Office
Microsoft Office 2016 kullan<PERSON><PERSON><PERSON>z ve KMS ile lisanslıyoruz.

## Outlook

### Ek Dosya Boyutu Artırma
Outlook ek limiti varsayılan olarak **20MB (20480)** dir.  
**50MB** yapmak için;

**Office 2016 ve 2019 için**
```cmd
REG ADD HKCU\Software\Microsoft\Office\16.0\Outlook\Preferences /v MaximumAttachmentsize /t REG_DWORD /d 51200
```
Önceden bu değer var ise çıkan **Value MaximumAttachmentsize exists, overwrite(Yes/No)?** uyarısına `Y/Yes` yazın ve `↳ Enter` tuşuna basın.

### PST Dosya Boyutu Artırma
Outlook ek limiti varsayılan olarak **50GB (51200)** dir.  
**100GB** yapmak için;

**Office 2016 ve 2019 için**
```cmd
REG ADD HKCU\Software\Microsoft\Office\16.0\Outlook\PST /v MaxLargeFileSize /t REG_DWORD /d 102400 /f
REG ADD HKCU\Software\Microsoft\Office\16.0\Outlook\PST /v WarnLargeFileSize /t REG_DWORD /d 97280 /f
```

Bu alana MB değeri girerek karşılıklarına göre yukarıdaki değerlerde değişiklik yapabilirsiniz.  
<input type="text" oninput="mbConverter(this)" placeholder="megabyte değer" value="40"/>&nbsp;<span id="mbConverter">40 = 40960</span>

### Otomatik Gönder/Al Süresi
Outlook varsayılan Gönder/Al süresi **30** dakikadır. Bu değeri değiştirmek için `Outlook` ekranında `CTRL` + `ALT` + `S` tuşlarına basın.  
Her 30 dakikada bir otomatik gönderme/alma gerçekleşsin seçeneğine tıklayın ve yeni bir dakika değer girin.

### Outlook Takılı Kalma
**Office Uygulamaları İşleniyor da (Başlangıç Ekranı) takılı kalırsa**
`Çalıştır > Outlook.exe / Safe` Açıldıktan sonra kapatıp tekrar aç

## Excel

### Fonksiyonlar
**Fonksiyon Ekleme**  
Excel de `ALT` + `F11` tuşuna basın. Açılan pencerede `Insert` > `Module` tıklayın. Eklenen fonksiyon **sadece** o Excel dosyasında çalışır.

**Fonksiyonu Excel Eklenti Yapma**
- Fonksiyonu tüm Excel dosyalarında çalıştırabilmek için fonksiyon eklendikten sonra dosyayı `Excel Eklentisi` olarak `(xlam)` kaydedin.
- Kaydedilen `Excel Eklentisi` dosyasına `Sağ Tık` > `Özellikler` > `Ayrıntılar` sekmesinden `Başlık` ve `Açıklamalar` girin.
- Varsayılan olarak dosya `%appdata%\Microsoft\AddIns` klasörüne kaydedilir.
- Kayıt işleminden sonra `Dosya` > `Seçenekler` > `Eklentiler` ekranındaki altta `Yönet (Excel Eklentileri) Git` seçeneği ile açılan pencerede kaydettiğiniz `Excel Eklentisi` seçeneğini aktif hale getirin.
- Burada sadece `%appdata%\Microsoft\AddIns` klasörü içindekiler listelenir. `Excel Eklentisi` ni farklı bir dizine kaydettiyseniz `Gözat` ile dosya yolunu belirtin.

#### SQL Guid Fonksiyonu
>=CreateGUID()

BD9532CE-E8BD-3B93-9B52-8B7B0CAE851C gibi SQL Guid standardında rastgele çıktı üretir.  

```vb
Public Function CreateGUID() As String    
    Do While Len(CreateGUID) < 32
        If Len(CreateGUID) = 16 Then
            CreateGUID = CreateGUID & Hex$(8 + CInt(Rnd * 3))
        End If
        CreateGUID = CreateGUID & Hex$(CInt(Rnd * 15))
    Loop
    CreateGUID = Mid(CreateGUID, 1, 2) & Hex$(Format(Now(), "hhnnss")) & "-" & Mid(CreateGUID, 9, 4) & "-" & Mid(CreateGUID, 13, 4) & "-" & Mid(CreateGUID, 17, 4) & "-" & Mid(CreateGUID, 21, 12)
End Function
```

#### Döviz Çekme Fonksiyonu
>=GetCurrency(Tarih; Kur; Ondalık; Tür; Geçmiş)

Tarihe göre TCMB den ilgili döviz kurunu getirir.  

- Tarihi tırnak içinde yazılmalıdır. `"15.11.2023"`
- Tarih herhangi bir hücreden seçilebilir. `B1`
- Kur tırnak içinde yazılmalıdır ve https://www.tcmb.gov.tr/kurlar/today.xml üzerindeki `Döviz Kodu` alanı ile eşleşmelidir. `"USD"` `"EUR"`
- Ondalık hanesi varsayılan olarak `2` dir. `28.7992` olan kur değerini `28.80` olarak yuvarlar.
- Tür tırnak içerisinde yazılmalıdır. Varsayılan olarak `"BanknoteSelling"` dir. Diğer seçenekler;
	- ForexBuying
	- ForexSelling
	- BanknoteBuying
	- BanknoteSelling
- Geçmiş varsayılan olarak `7` gündür. Hafta sonları ve resmi tatil günleri gibi kur değeri olmayan günlerlerden geriye doğru bakılacak gün sayısı belirtilir.

```vb
Public Function GetCurrency(curDate As Date, curName As String, Optional curDecimal As Variant, Optional curType As Variant, Optional curHistory As Variant) As String
    Dim xmlDoc As Object
    Dim xmlElem As Object
    Set xmlDoc = CreateObject("MSXML2.DOMDocument")
    xmlDoc.async = False
    xmlDoc.validateOnParse = False
    
    If IsMissing(curHistory) Then curHistory = 7
    If IsMissing(curDecimal) Then curDecimal = 2
    If IsMissing(curType) Then curType = "BanknoteSelling"
    
    dateCounter = 0
    dateDay = Format(curDate, "dd")
    dateMonth = Format(curDate, "mm")
    dateYear = Format(curDate, "yyyy")
LogicLine:
    If (dateCounter > curHistory) Then
        GetCurrency = "#YOK"
    Else
        If Not xmlDoc.Load("https://www.tcmb.gov.tr/kurlar/" & dateYear & dateMonth & "/" & dateDay & dateMonth & dateYear & ".xml") Then
            dateCounter = dateCounter + 1
            
            If (dateDay < 1) Then
                dateDay = 31
                dateMonth = Format(dateMonth - 1, "00")
                
                If (dateMonth < 1) Then
                    dateMonth = 12
                    dateYear = Format(dateYear - 1, "0000")
                End If
            Else
                dateDay = Format(dateDay - 1, "00")
            End If
            GoTo LogicLine
        Else
            Set xmlElem = xmlDoc.DocumentElement
            curVal = xmlElem.SelectSingleNode("Currency[@CurrencyCode='" & curName & "']").SelectSingleNode("" & curType & "").Text
            curVal = Round(Replace(curVal, ".", ","), curDecimal)
        End If
        GetCurrency = curVal
    End If
End Function
```

### Makrolar
?> Excel 'de Makro veya Fonksiyon eklendiğinde dosyayı *Makro İçerebilen Excel Çalışma Kitabı* olarak **(xlsm)** kaydedin.

#### Outlook Backup
```vb
Sub Backup()
    Dim olApp As Outlook.Application
    Dim objNS As Outlook.NameSpace
    Dim objSourceFolder As Outlook.MAPIFolder
    Dim yearInput As String
    Dim folderPath As String
    Dim newPSTFileName As String
    Dim newPSTFilePath As String
    Dim objPST As Outlook.MAPIFolder
    Dim objTargetFolder As Outlook.MAPIFolder
    Dim objBackupFolder As Outlook.MAPIFolder
    Dim folderNames As Variant
    Dim subFolder As Outlook.MAPIFolder
    Dim foundItems As Integer
    Dim newFolder As Outlook.MAPIFolder
    Dim olItms As Outlook.Items
    Dim olMail As Object
    
    Set olApp = Outlook.Application
    Set objNS = olApp.GetNamespace("MAPI")
    Set objSourceFolder = objNS.PickFolder
    
    If Not objSourceFolder Is Nothing Then
        yearInput = InputBox("Bir yıl girin (örneğin, 2023):", "Yıl Girişi")
        If IsNumeric(yearInput) And Len(yearInput) = 4 Then
            foundItems = FindEmail(objSourceFolder, yearInput)
            
            If foundItems = 0 Then
                MsgBox "Seçilen yılda herhangi bir e-posta bulunamadı. PST dosyası oluşturulmadı."
                Exit Sub
            End If
            
            folderPath = Environ("USERPROFILE") & "\Documents\Outlook Dosyaları\"
            newPSTFileName = yearInput & ".pst"
            newPSTFilePath = folderPath & newPSTFileName
            
            On Error Resume Next
            objNS.AddStore newPSTFilePath
            If Err.Number = 0 Then
                Set objPST = objNS.Folders.GetLast
                objPST.Name = yearInput
            Else
                MsgBox "PST dosyası oluşturulurken bir hata oluştu: " & Err.Description
                Exit Sub
            End If
            On Error GoTo 0
            
            Set objTargetFolder = objNS.Folders(yearInput)
            If Not objTargetFolder Is Nothing Then
                Set objBackupFolder = objPST.Folders.Add("Backup", olFolderInbox)
                
                folderNames = Array("Gelen Kutusu", "Gönderilmiş Öğeler", "Silinmiş Öğeler", "Taslaklar", "Önemsiz E-Posta")
                
                For Each subFolder In objSourceFolder.Folders
                    If IsInArray(subFolder.Name, folderNames) Then
                        Set newFolder = objBackupFolder.Folders.Add(subFolder.Name, olFolderInbox)
                        
                        Set olItms = subFolder.Items
                        restrictionSentOn = "[SentOn] >= '" & yearInput & ".01.01 00:00' AND [SentOn] <= '" & yearInput & ".12.31 23:59'"
                        restrictionReceivedTime = "[ReceivedTime] >= '" & yearInput & ".01.01 00:00' AND [ReceivedTime] <= '" & yearInput & ".12.31 23:59'"
                        
                        Set olMail = olItms.Find(restrictionSentOn)
                        While Not olMail Is Nothing
                            olMail.Move newFolder
                            Set olMail = olItms.FindNext
                        Wend
                        
                        Set olMail = olItms.Find(restrictionReceivedTime)
                        While Not olMail Is Nothing
                            olMail.Move newFolder
                            Set olMail = olItms.FindNext
                        Wend
                        
                        CopyStructure subFolder, newFolder, yearInput
                    End If
                Next subFolder
                
                MsgBox "Klasör yapısı ve mailler başarıyla YEDEK altına taşındı."
            Else
                MsgBox "Hedef klasör oluşturulurken bir hata oluştu."
            End If
        Else
            MsgBox "Geçerli bir yıl girin."
        End If
    Else
        MsgBox "Hiçbir klasör seçilmedi."
    End If
    
    Set objSourceFolder = Nothing
    Set objTargetFolder = Nothing
    Set objNS = Nothing
    Set olApp = Nothing
End Sub

Function FindEmail(folder As Outlook.MAPIFolder, yearInput As String) As Integer
    Dim olItms As Outlook.Items
    Dim olMail As Object
    Dim restrictionSentOn As String
    Dim restrictionReceivedTime As String
    Dim subFolder As Outlook.MAPIFolder
    Dim count As Integer
    Dim allowedFolders As Variant

    allowedFolders = Array("Gelen Kutusu", "Gönderilmiş Öğeler", "Silinmiş Öğeler", "Taslaklar", "Önemsiz E-Posta")
    count = 0
    
    If IsInArray(folder.Name, allowedFolders) Then
        Set olItms = folder.Items
        restrictionSentOn = "[SentOn] >= '" & yearInput & ".01.01 00:00' AND [SentOn] <= '" & yearInput & ".12.31 23:59'"
        restrictionReceivedTime = "[ReceivedTime] >= '" & yearInput & ".01.01 00:00' AND [ReceivedTime] <= '" & yearInput & ".12.31 23:59'"
        
        Set olMail = olItms.Find(restrictionSentOn)
        While Not olMail Is Nothing
            count = count + 1
            Set olMail = olItms.FindNext
        Wend
        
        Set olMail = olItms.Find(restrictionReceivedTime)
        While Not olMail Is Nothing
            count = count + 1
            Set olMail = olItms.FindNext
        Wend
    End If

    For Each subFolder In folder.Folders
        count = count + FindEmail(subFolder, yearInput)
    Next subFolder

    FindEmail = count
End Function

Function IsInArray(stringToBeFound As String, arr As Variant) As Boolean
    Dim element As Variant
    IsInArray = False
    For Each element In arr
        If element = stringToBeFound Then
            IsInArray = True
            Exit Function
        End If
    Next element
End Function

Sub CopyStructure(srcFolder As Outlook.MAPIFolder, destFolder As Outlook.MAPIFolder, yearInput As String)
    Dim subFolder As Outlook.MAPIFolder
    Dim newFolder As Outlook.MAPIFolder
    Dim olItms As Outlook.Items
    Dim olMail As Object
    Dim restrictionSentOn As String
    Dim restrictionReceivedTime As String

    For Each subFolder In srcFolder.Folders
        Set newFolder = destFolder.Folders.Add(subFolder.Name, olFolderInbox)
        
        Set olItms = subFolder.Items
        restrictionSentOn = "[SentOn] >= '" & yearInput & ".01.01 00:00' AND [SentOn] <= '" & yearInput & ".12.31 23:59'"
        restrictionReceivedTime = "[ReceivedTime] >= '" & yearInput & ".01.01 00:00' AND [ReceivedTime] <= '" & yearInput & ".12.31 23:59'"
        
        Set olMail = olItms.Find(restrictionSentOn)
        While Not olMail Is Nothing
            olMail.Move newFolder
            Set olMail = olItms.FindNext
        Wend
        
        Set olMail = olItms.Find(restrictionReceivedTime)
        While Not olMail Is Nothing
            olMail.Move newFolder
            Set olMail = olItms.FindNext
        Wend

        CopyStructure subFolder, newFolder, yearInput
    Next subFolder
End Sub
```

#### SQLInsert
?> A1 = tableName  
B1 = columnCount

```vb
Public Sub SQLInsert()
    Dim lastRow As Long
    Dim lastColumn As Long
    Dim sqlQuery As String
    Dim x As Long
    Dim y As Long
    Dim fileName As String
    Dim fileNumber As Integer
    Dim timestamp As String
    Dim cellText As String
    
    lastColumn = Cells(1, 2).Value
    lastRow = Cells(Rows.Count, 1).End(xlUp).Row
    
    timestamp = Format(Now, "yyyyMMdd_HHmmss")
    fileName = Environ("USERPROFILE") & "\Desktop\Insert_" & Cells(1, 1).Value & "_" & timestamp & ".sql"
    fileNumber = FreeFile
    Open fileName For Output As fileNumber
    
    For y = 3 To lastRow
        sqlQuery = "INSERT INTO " & Cells(1, 1).Value & " ("
        For x = 1 To lastColumn
            sqlQuery = sqlQuery & Cells(2, x).Value & ", "
        Next x
        sqlQuery = Left(sqlQuery, Len(sqlQuery) - 2) & ") VALUES ("
        
        For x = 1 To lastColumn
            cellText = Replace(Cells(y, x).Text, vbLf, "")
            
            If cellText = "NULL" Then
                sqlQuery = sqlQuery & cellText & ", "
            Else
                sqlQuery = sqlQuery & "'" & cellText & "', "
            End If
        Next x
        sqlQuery = Left(sqlQuery, Len(sqlQuery) - 2) & ");"
        'Debug.Print sqlQuery
        Print #fileNumber, sqlQuery
    Next y
    
    Close fileNumber
    Shell "notepad.exe " & fileName, vbNormalFocus
End Sub
```

#### SQLUpdate
?> A1 = tableName  
C1 = setField

```vb
Public Sub SQLUpdate()
    Dim lastRow As Long
    Dim lastColumn As Long
    Dim sqlQuery As String
    Dim x As Long
    Dim y As Long
    Dim fileName As String
    Dim fileNumber As Integer
    Dim timestamp As String
    Dim cellText As String
    
    lastColumn = Cells(1, 2).Value
    lastRow = Cells(Rows.Count, 1).End(xlUp).Row
    
    timestamp = Format(Now, "yyyyMMdd_HHmmss")
    fileName = Environ("USERPROFILE") & "\Desktop\Update_" & Cells(1, 1).Value & "_" & timestamp & ".sql"
    fileNumber = FreeFile
    Open fileName For Output As fileNumber
    
    For y = 3 To lastRow
        sqlQuery = "UPDATE " & Cells(1, 1).Value & " SET " & Cells(2, 3).Value & "='" & Cells(y, 3).Value & "' WHERE Id='{" & Cells(y, 1) & "}';"
        'Debug.Print sqlQuery
        Print #fileNumber, sqlQuery
    Next y
    
    Close fileNumber
    Shell "notepad.exe " & fileName, vbNormalFocus
End Sub
```

#### JSON
?> A1 = tableName  
B1 = columnCount

```vb
Public Sub JSON()
    Dim lastRow As Long
    Dim lastColumn As Long
    Dim jsonContent As String
    Dim x As Long
    Dim y As Long
    Dim fileName As String
    Dim fileNumber As Integer
    Dim timestamp As String
    Dim cellText As String
    
    lastColumn = Cells(1, 2).Value
    lastRow = Cells(Rows.Count, 1).End(xlUp).Row
    
    timestamp = Format(Now, "yyyyMMdd_HHmmss")
    fileName = Environ("USERPROFILE") & "\Desktop\" & Cells(1, 1).Value & "_" & timestamp & ".json"
    fileNumber = FreeFile
    Open fileName For Output As fileNumber
    
    Print #fileNumber, "{""" & Cells(1, 1).Value & """: ["
    
    For y = 3 To lastRow
        jsonContent = "{"
        
        For x = 1 To lastColumn
            jsonContent = jsonContent & """" & Cells(2, x).Value & """" & ":"
            cellText = Replace(Cells(y, x).Text, vbLf, "")
            
            If cellText = "NULL" Then
                jsonContent = jsonContent & "null"
            Else
                jsonContent = jsonContent & """" & cellText & """"
            End If
            
            If x < lastColumn Then
                jsonContent = jsonContent & ","
            End If
        Next x
        
        jsonContent = jsonContent & "}"
        
        If y < lastRow Then
            jsonContent = jsonContent & ","
        End If
        
        Print #fileNumber, jsonContent
    Next y
    
    Print #fileNumber, "]}"
    
    Close fileNumber
    Shell "notepad.exe " & fileName, vbNormalFocus
End Sub
```

### Grup Elamanlarına Göre Yeni Sekme Oluşturma
```vb
Sub AddSheet()
    Dim ws As Worksheet
    Dim wsNew As Worksheet
    Dim cell As Range
    Dim ref As Range
    Dim val As String
    Dim lastRow As Long
    Dim dict As Object
    
    Set ws = ThisWorkbook.ActiveSheet
    Set ref = ws.Range("H:H");
    lastRow = ws.Cells(ws.Rows.Count, ref.Column).End(xlUp).Row
    Set dict = CreateObject("Scripting.Dictionary")
    
    For Each cell In ws.Range(ref.Cells(2, 1), ref.Cells(lastRow, 1))
        val = cell.Value
        If val <> "" And Not dict.exists(val) Then
            Set wsNew = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
            wsNew.Name = val
            dict.Add val, wsNew
            ws.Rows(1).Copy Destination:=wsNew.Rows(1)
        End If
        
        If dict.exists(val) Then
            ws.Rows(cell.Row).Copy Destination:=dict(val).Cells(dict(val).Rows.Count, 1).End(xlUp).Offset(1, 0)
        End If
    Next cell
    
    MsgBox "İşlem Tamamlandı!"
End Sub
```

### Formatlar
SQL Datetime formatı `yyyy-aa-gg ss:dd:nn`


## Word
### Tarih Ekleme
`ALT + SHIFT + D`

### Saat Ekleme
`ALT + SHIFT + T`