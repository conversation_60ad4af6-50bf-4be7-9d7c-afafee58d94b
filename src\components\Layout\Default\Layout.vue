<template>
   <div
      class="h-full"
      id="wrapper">
      <v-app v-bind:class="theme.current.value.dark ? 'bg-dark' : 'bg-light'">
         <DrawerBar />
         <DrawerMenu />
         <Header />

         <v-main class="h-full transition-none">
            <ProgressBar />

            <div
               class="v-main__scroller h-full overflow-y-scroll scroll-smooth"
               id="main">
               <router-view
                  v-bind:key="$route.fullPath"
                  v-slot="{ Component }">
                  <v-fade-transition leave-absolute>
                     <component v-bind:is="Component" />
                  </v-fade-transition>
               </router-view>
            </div>
         </v-main>

         <ConfirmDialog />
         <PromptDialog />
      </v-app>

      <v-snackbar-queue
         v-model="messages.queue"
         v-bind:closable="true"
         class="p-0" />
   </div>
</template>

<script lang="ts" setup>
import ConfirmDialog from "./Dialog/ConfirmDialog.vue";
import PromptDialog from "./Dialog/PromptDialog.vue";
import DrawerBar from "./Drawer/DrawerBar.vue";
import DrawerMenu from "./Drawer/DrawerMenu.vue";
import Header from "./Header/Header.vue";
import ProgressBar from "./Loader/ProgressBar.vue";

const messages = useMessageStore();
const theme = useTheme();
</script>
