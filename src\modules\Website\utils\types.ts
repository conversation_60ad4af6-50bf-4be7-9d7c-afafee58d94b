export interface ICategory extends IDefaultFields {
   id: number;
   code: string;
   title: string;
   content: string;
   image_path: string;
   is_active: number;
   sort_order: number;
   parent_id: number;
   group_id: number;
   url: string;
   meta_title: string;
   meta_description: string;
   meta_keywords: string;
}

export interface ICategoryStore {
   id?: number;
   category_id?: number;
   code: string;
   image_path?: string;
   is_active?: number;
   sort_order?: number;
   parent_id?: number;
   group_id?: number;
   translate?: ITranslate[];
}