<?php

declare(strict_types=1);

namespace {namespace};

use OpenApi\Generator;

/**
 * @OA\Info(
 *    title="Swagger",
 *    version="1.0.0",
 * )
 * @OA\SecurityScheme(
 *    type="apiKey",
 *    name="Authorization",
 *    in="header",
 *    scheme="bearer",
 *    securityScheme="Bearer",
 *    bearerFormat="JWT",
 * )
 * @OA\OpenApi(security={{"Bearer": {}}})
 */
class {class} {
   public function json() {
      $openapi = Generator::scan([$_SERVER['DOCUMENT_ROOT'] . '/App/Modules/{module}/Controllers']);
      header('Content-Type: application/json; charset=UTF-8');
      print($openapi->toJson());
   }
}
