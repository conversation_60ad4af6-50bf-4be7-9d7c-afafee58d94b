<template>
   <v-data-table
      v-bind="{ ...$attrs }"
      v-bind:class="{
         'v-table--sticky-header': props.stickyHeader,
         'v-table--sticky-footer': props.stickyFooter,
         'v-table--accent-header': props.accentHeader
      }"
      v-bind:disable-sort="disableSort"
      v-bind:headers="props.headers"
      v-bind:items="filteredItems"
      v-bind:items-per-page-options="[10, 25, 50, 100, -1]"
      v-bind:loading="props.loading || optionsLoading"
      density="compact"
      hover
      items-per-page="10"
      return-object
      @update:options="handleOptionsUpdate"
      @update:page="expandedItems = []">
      <template v-slot:loading>
         <v-skeleton-loader type="table-row-divider, list-item-three-line, list-item-two-line, list-item-two-line" />
      </template>

      <template v-slot:headers="{ columns, isSorted, getSortIcon, toggleSort, someSelected, allSelected, selectAll }">
         <TableHeader v-bind="{ columns, isSorted, getSortIcon, toggleSort, someSelected, allSelected, selectAll, disableSort }" />
      </template>

      <template v-slot:body="{ internalItems, isSelected, toggleSelect, columns }: { [key: string]: any }">
         <v-fade-transition
            name="table"
            group
            hide-on-leave>
            <template
               v-for="item in internalItems"
               :key="item.value.id">
               <tr
                  v-bind:class="{ 'v-data-table__tr--clickable': !props.ripple && rowClick }"
                  v-ripple="!!appConfig.default.ripple && !!props.ripple && !!rowClick"
                  class="v-data-table__tr"
                  @click="() => props.rowClick && props.rowClick(item.value)"
                  @click.ctrl.stop="(!itemExpand || item.value[itemExpand]) && toggleExpand(item)">
                  <template v-for="(column, index) in Object.keys(item.columns)">
                     <td
                        v-if="column === 'data-table-select'"
                        class="v-data-table__td v-data-table-column--no-padding v-data-table-column--align-start v-data-table__td--select-row">
                        <v-checkbox-btn
                           v-bind:disabled="!item.selectable"
                           v-bind:model-value="isSelected(item)"
                           v-ripple.stop
                           class="text-base"
                           @click.stop="toggleSelect(item)" />
                     </td>

                     <td
                        v-else-if="column === 'data-table-expand'"
                        class="v-data-table__td v-data-table-column--no-padding v-data-table-column--align-start v-data-table__td--expanded-row">
                        <div class="flex justify-end">
                           <v-btn
                              v-bind:disabled="!!itemExpand && !item.value[itemExpand]"
                              v-ripple.stop
                              icon
                              @click.stop="(!itemExpand || item.value[itemExpand]) && toggleExpand(item)">
                              <v-icon
                                 v-bind:class="{ 'rotate-180': isExpanded(item) }"
                                 class="transition duration-150"
                                 icon="$expand" />
                           </v-btn>

                           <slot name="action" />
                        </div>
                     </td>

                     <td
                        v-else
                        v-bind:class="`v-data-table-column--align-${columns[index].align || 'start'}`"
                        v-bind:data-label="columns[index].title"
                        class="v-data-table__td">
                        <slot
                           v-bind:column="columns[index]"
                           v-bind:item="item.value"
                           v-bind:name="`item.${column}`"
                           v-bind:value="item.columns[column]">
                           <template v-if="columns[index].format">
                              {{ formatDate(item.columns[column], columns[index].format) }}
                           </template>
                           <template v-else>
                              {{ item.columns[column] }}
                           </template>
                        </slot>
                     </td>
                  </template>
               </tr>

               <tr
                  v-if="isExpanded(item)"
                  v-bind:key="`expand-${item.value.id}`"
                  class="v-data-table__tr--expand v-data-table__tr">
                  <td v-bind:colspan="columns.length">
                     <slot
                        v-bind:item="item.value"
                        name="expand" />
                  </td>
               </tr>
            </template>
         </v-fade-transition>
      </template>
   </v-data-table>
</template>

<style>
@media screen and (max-width: 600px) {
   .v-data-table td {
      position: relative;
      /* display: inline-flex; */
      display: flex;
      /* align-items: flex-end; */
      align-items: center;
      justify-content: flex-end;
      /* width: 50%; */
   }

   .v-data-table td::before {
      content: attr(data-label);
      position: absolute;
      left: 8px;
      top: 0;
      font-size: 12px;
      /* line-height: 2em; */
      opacity: 0.5;

      display: flex;
      align-items: center;
      height: 100%;
   }

   .v-data-table tr {
      border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
   }

   .v-data-table td {
      border-bottom: 0;
   }
}
</style>

<script lang="ts" setup>
import type { TDataTable } from "@/utils/types";
import TableHeader from "./TableHeader.vue";

type TProps = {
   items?: any[];
   stickyHeader?: boolean;
   stickyFooter?: boolean;
   accentHeader?: boolean;
   disableSort?: boolean;
   rowClick?: (item: any) => any;
   itemExpand?: string;
   multiExpand?: boolean;
   loading?: boolean;
   filter?: string;
   headers?: any[];
   ripple?: boolean;
};

const props = withDefaults(defineProps<TDataTable & TProps>(), {
   stickyHeader: true,
   stickyFooter: true,
   accentHeader: false,
   disableSort: false,
   filter: "",
   ripple: false
});

// const filteredItems = computed(() => {
//    const value = lowerCase(props.filter);
//    const headers = props.headers?.map((h) => h.key);
//    return props.items?.filter((item: any) => {
//       // return !(value && !inputFilter(lowerCase(Object.values(item).join(" ").toString()), value));
//       // return !(value && !inputFilter(lowerCase(props.headers?.map((header: any) => item[header.key]).join(" ")), value));
//       return !(value && !inputFilter(lowerCase(headers?.map((key: any) => item[key]).join(" ")), value));
//    });
// });

const filteredItems = computed(() => {
   const filter = lowerCase(props.filter);
   const keys = props.headers?.map((h) => h.key);

   return props.items
      ?.map((item) => {
         const headers = lowerCase(keys?.map((key: any) => item[key]).join(" "));
         return { ...item, headers };
      })
      .filter((item) => {
         if (!filter) return true;
         return inputFilter(item.headers, filter);
      });
});

const expandedItems: any = ref([]);
const isExpanded = (item: any) => expandedItems.value.includes(item);
const toggleExpand = (item: any, multiple: boolean = props.multiExpand) => {
   if (multiple) {
      if (isExpanded(item)) {
         expandedItems.value.splice(expandedItems.value.indexOf(item), 1);
      } else {
         expandedItems.value.push(item);
      }
   } else {
      expandedItems.value = isExpanded(item) ? [] : [item];
   }
};

const optionsLoading = ref(false);
const currentOptions: any = ref({});
const handleOptionsUpdate = async (options: any) => {
   if (options.itemsPerPage !== currentOptions.value.itemsPerPage) {
      optionsLoading.value = true;
      await nextTick();
   }

   setTimeout(() => (optionsLoading.value = false), 25);
   currentOptions.value = options;
};
</script>
