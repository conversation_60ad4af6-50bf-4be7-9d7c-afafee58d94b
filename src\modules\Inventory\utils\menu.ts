export const productMenu: TList[] = [
   {
      itemType: "subheader",
      itemTitle: "module.inventory"
   },
   {
      itemType: "divider"
   },
   {
      itemTitle: "app.product",
      itemProps: {
         to: "/inventory/product",
         prependIcon: "$product"
      }
   },
   {
      itemTitle: "app.competing",
      itemProps: {
         to: "/inventory/competing",
         prependIcon: "$competing"
      }
   },
   {
      itemTitle: "app.definitions",
      itemProps: {
         prependIcon: "$definitions",
         value: "definitions"
      },
      children: [
         {
            itemTitle: "app.category",
            itemProps: {
               to: "/inventory/category"
            }
         },
         {
            itemTitle: "app.manufacturer",
            itemProps: {
               to: "/inventory/manufacturer"
            }
         },
         {
            itemTitle: "app.standard",
            itemProps: {
               to: "/inventory/standard"
            }
         },
         {
            itemTitle: "app.attribute",
            itemProps: {
               to: "/inventory/attribute"
            }
         }
      ]
   }
];
