// icons
// import { mdi } from "vuetify/iconsets/mdi-svg";

// colors
// import tailwindColors from "./tailwind3";
// import vuetifyColors from "vuetify/util/colors";
import { createVueI18nAdapter } from "vuetify/locale/adapters/vue-i18n";
// vuetify
// import tailwindColors from "tailwindcss/colors";
// import colors from 'vuetify/util/colors'
import * as labsComponents from "vuetify/labs/components";

const colorScheme = {
   light: {
      colors: {
         error: tailwind3.red["600"],
         info: tailwind3.violet["600"],
         success: tailwind3.emerald["600"],
         warning: tailwind3.amber["600"],
         "on-surface": tailwind3.neutral["800"],
         "on-background": tailwind3.neutral["800"]
      }
   },
   dark: {
      colors: {
         "on-surface": tailwind3.neutral["200"],
         "on-background": tailwind3.neutral["200"]
      }
   }
};

export const vuetify = createVuetify({
   components: {
      ...labsComponents
   },
   locale: {
      adapter: createVueI18nAdapter({ i18n, useI18n })
   },
   theme: {
      defaultTheme: getUserTheme(),
      themes: {
         ...colorScheme
      }
   },
   defaults: {
      global: {
         ripple: appConfig.default.ripple
      },
      VTabs: {
         density: "compact"
      },
      VSkeletonLoader: {
         type: "subtitle, divider, text@2, paragraph, subtitle, paragraph"
      },
      VToolbar: {
         flat: true,
         density: "compact"
      },
      VBreadcrumbs: {
         density: "compact"
      },
      VDialog: {
         scrim: "rgb(var(--v-theme-on-surface-bright))",
         persistent: true,
         noClickAnimation: true,

         VCard: {
            elevation: 8
         },
         VToolbar: {
            density: "compact"
         },
         VCardActions: {
            // VBtn: {
            //    density: "default"
            // }
         }
      },
      VIcon: {
         size: "small"
      },
      VBtn: {
         variant: "text",
         density: "comfortable"
      },
      VDatePicker: {
         VBtn: {
            density: "default"
         }
      },
      VTextField: {
         clearable: true,
         density: "compact",
         variant: "outlined"
      },
      VTextarea: {
         clearable: true,
         density: "compact",
         variant: "outlined"
      },
      VMenu: {
         // scrim: "rgb(var(--v-theme-on-surface-bright))"
      },
      VSelect: {
         clearable: true,
         density: "compact",
         variant: "outlined",
         eager: true,
         // menuProps: { scrim: "rgb(var(--v-theme-on-surface-bright))" }

         VList: {
            density: "compact"
         }
      },
      VAutocomplete: {
         clearable: true,
         density: "compact",
         variant: "outlined",
         eager: true
      },
      VSheet: {
         border: true,
         rounded: true
      },
      VList: {
         lines: false,
         nav: true,
         slim: true,
         density: "compact"
      },
      VCheckbox: {
         color: "primary",

         VIcon: {
            size: "default"
         }
      },
      VCheckboxBtn: {
         color: "primary",
         density: "comfortable"
      },
      VRadioGroup: {
         color: "primary",

         VIcon: {
            size: "default"
         }
      },
      VDataTableFooter: {
         VSelect: {
            clearable: false
         }
      },
      VProgressLinear: {
         color: "primary"
      },
      VCol: {
         cols: 12
      }
   },
   icons: {
      defaultSet: "tabler",
      aliases: {
         // ...aliases,
         ...tablerAliases
         // ...phoAliases
      },
      sets: {
         tabler
      }
   }
});
