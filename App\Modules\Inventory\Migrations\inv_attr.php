<?php

declare(strict_types=1);

use System\Migration\Migration;

class inv_attr extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `inv_attr` (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `sort_order` INT NOT NULL DEFAULT 0,
         {$this->defaults()}
      )");

      $this->database->table('inv_attr')->insert([
         'sort_order' => 1,
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `inv_attr`");
   }
}
