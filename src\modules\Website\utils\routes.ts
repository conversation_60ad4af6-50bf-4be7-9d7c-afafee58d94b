import DefaultLayout from "@/components/Layout/Default/Layout.vue";

export const websiteRoutes: RouteRecordRaw[] = [
   {
      path: "/website",
      meta: {
         layout: DefaultLayout,
         module: "Website"
      },
      children: [
         {
            path: "category",
            meta: {
               title: "app.category",
               breadcrumb: "website.categoryList"
            },
            children: [
               {
                  path: "",
                  component: getComponent(() => import("../pages/CategoryList.vue"))
               },
               {
                  path: ":id(create|[0-9]+)",
                  component: getComponent(() => import("../pages/CategoryDetail.vue")),
                  meta: {
                     breadcrumb: "{CategoryDetail}"
                  }
               }
            ]
         }
      ]
   }
];
