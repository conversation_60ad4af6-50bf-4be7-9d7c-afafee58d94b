<?php

declare(strict_types=1);

use System\Migration\Migration;

class inv_manufacturer extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `inv_manufacturer` (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `code` VARCHAR(50) NOT NULL,
         `image_path` VARCHAR(250) NULL DEFAULT NULL,
         `is_active` BOOLEAN NOT NULL DEFAULT 1,
         `sort_order` INT NOT NULL DEFAULT 0,
         `is_competing` BOOLEAN NOT NULL DEFAULT 0,
         {$this->defaults()}
      )");

      $this->database->table('inv_manufacturer')->insert([
         'code' => 'MAN001',
         'is_active' => 1,
         'sort_order' => 1,
      ])->prepare()->execute();

      $this->database->table('inv_manufacturer')->insert([
         'code' => 'MAN002',
         'is_active' => 1,
         'sort_order' => 2,
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `inv_manufacturer`");
   }
}
