<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Controllers;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Inventory\DTOs\StandardDTO;
use App\Modules\Inventory\Models\StandardModel;
use App\Modules\Inventory\Services\StandardService;

/**
 * @OA\Tag(name="Standard", description="Standart işlemleri")
 */
class StandardController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected StandardService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Standard"}, path="/standard/", summary="Standart listesi",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getAllStandard() {
      $this->response(function () {
         $result = $this->service->getAll($this->language(), StandardModel::class);
         return $result;
      });
   }

   /**
    * @OA\Get(tags={"Standard"}, path="/standard/{id}", summary="Standart detayı (ID'ye göre)",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer"))
    * )
    */
   public function getStandard(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getOne($id, $this->language(), StandardModel::class);
         return $result;
      });
   }

   /**
    * @OA\Post(tags={"Standard"}, path="/standard/", summary="Standart ekle",
    *    @OA\Response(response=201, description="Success"),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer")),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"image_path", "sort_order"},
    *       @OA\Property(property="image_path", type="string", example="/images/standards/standard1.jpg"),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="translate", type="array", @OA\Items(
    *          @OA\Property(property="language_id", type="integer", example="1"),
    *          @OA\Property(property="title", type="string", example="EN388"),
    *          @OA\Property(property="content", type="string", example="EN388 standartları açıklaması")
    *       ))
    *    ))
    * )
    */
   public function createStandard() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new StandardDTO();
         $dto->fromRequest($request);
         $result = $this->service->createStandard($dto, $this->language());
         return $result;
      }, code: 201);
   }

   /**
    * @OA\Put(tags={"Standard"}, path="/standard/", summary="Standart güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="lang_id", in="query", required=false, @OA\Schema(type="integer")),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"id", "image_path", "sort_order"},
    *       @OA\Property(property="id", type="integer", example=1),
    *       @OA\Property(property="image_path", type="string", example="/images/standards/standard1.jpg"),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="translate", type="array", @OA\Items(
    *          @OA\Property(property="language_id", type="integer", example="1"),
    *          @OA\Property(property="title", type="string", example="EN388"),
    *          @OA\Property(property="content", type="string", example="EN388 standartları açıklaması")
    *       ))
    *    ))
    * )
    */
   public function updateStandard() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new StandardDTO();
         $dto->fromRequest($request);
         $result = $this->service->updateStandard($dto, $this->language());
         return $result;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Standard"}, path="/standard/{id}", summary="Standart sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteStandard(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete(['id' => $id]);
         return $result;
      });
   }
}
