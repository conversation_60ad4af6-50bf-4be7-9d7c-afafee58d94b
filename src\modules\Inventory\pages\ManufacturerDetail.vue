<template>
   <Container v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ title }}</v-card-title>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               prepend-icon="$save"
               @click="form?.requestSubmit()">
               {{ t("app.save") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
            <LanguageTab
               v-model="language"
               v-bind:loading="isLoading" />
         </template>

         <v-card-text>
            <v-form
               ref="form"
               @submit.prevent="formHandler">
               <input
                  class="hidden"
                  type="submit" />
               <v-row no-gutters>
                  <v-col md="4">
                     <v-list-subheader>{{ t("app.code") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-text-field
                        v-model="manufacturer.code"
                        v-bind:rules="[appRules.required()]" />
                  </v-col>

                  <v-col md="4">
                     <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-text-field
                        v-model="manufacturer.title"
                        v-bind:rules="[appRules.required()]">
                        <template
                           v-if="language !== 1"
                           v-slot:append-inner>
                           <TranslateButton v-model="manufacturer.title" />
                        </template>
                     </v-text-field>
                  </v-col>

                  <v-col md="4">
                     <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-textarea
                        v-model="manufacturer.content"
                        v-bind:rules="[appRules.required()]"
                        auto-grow
                        no-resize>
                        <template
                           v-if="language !== 1"
                           v-slot:append-inner>
                           <TranslateButton v-model="manufacturer.content" />
                        </template>
                     </v-textarea>
                  </v-col>

                  <v-col md="4">
                     <v-list-subheader>{{ t("inventory.isCompeting") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-switch
                        v-model="manufacturer.is_competing"
                        v-bind:false-value="0"
                        v-bind:ripple="false"
                        v-bind:true-value="1"
                        color="primary"
                        density="compact">
                        <template v-slot:label>
                           <div class="text-sm">{{ manufacturer.is_competing ? t("app.yes") : t("app.no") }}</div>
                        </template>
                     </v-switch>
                  </v-col>

                  <v-col md="4">
                     <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <v-switch
                        v-model="manufacturer.is_active"
                        v-bind:false-value="0"
                        v-bind:ripple="false"
                        v-bind:true-value="1"
                        color="primary"
                        density="compact">
                        <template v-slot:label>
                           <div class="text-sm">{{ manufacturer.is_active ? t("app.active") : t("app.passive") }}</div>
                        </template>
                     </v-switch>
                  </v-col>

                  <v-col md="4">
                     <v-list-subheader>{{ t("app.image") }}</v-list-subheader>
                  </v-col>
                  <v-col md="8">
                     <ImageList
                        v-bind:delete="deleteImageHandler"
                        v-bind:items="[manufacturer.image_path]" />
                     <ImageUpload v-model="imageUpload" />
                  </v-col>
               </v-row>
            </v-form>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import ImageList from "@/components/Input/ImageList.vue";
import ImageUpload from "@/components/Input/ImageUpload.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { useDeleteImage, useUploadImage } from "../services/ImageService";
import { useCreateManufacturer, useGetManufacturerById, useUpdateManufacturer } from "../services/ManufacturerService";
import { IManufacturer, IManufacturerStore } from "../utils/types";

const { t } = useI18n();
const route = useRoute() as IRoute;
const appStore = useAppStore();
const confirmStore = useConfirmStore();
const snackbar = useMessageStore();

// manufacturer
const form = ref<HTMLFormElement>();
const manufacturer = ref({
   is_active: 1,
   is_competing: 0
} as IManufacturer);
const routeId = computed(() => route.params.id);
const isCreate = computed(() => routeId.value === "create");
const isEnabled = computed(() => !!routeId.value && !isCreate.value);
const language = ref(1);
const title = computed(() => (isCreate.value ? t("inventory.createManufacturer") : t("inventory.manufacturerDetail")));
const imageUpload = ref([] as File[]);

// set breadcrumb
appStore.setBreadcrumb("ManufacturerDetail", title);

// services
const getManufacturerById = useGetManufacturerById({
   id: routeId,
   enabled: isEnabled,
   language: language,
   onSuccess: (data) => {
      manufacturer.value = { ...data };
   }
});
const updateManufacturer = useUpdateManufacturer();
const createManufacturer = useCreateManufacturer();
const uploadImage = useUploadImage();
const deleteImage = useDeleteImage({ invalidate: ["manufacturer", "manufacturerById"] });

// loading
const isLoading = computed(() => getManufacturerById.isLoading.value);
const isPending = computed(() => createManufacturer.isPending.value || updateManufacturer.isPending.value);

// handlers
const deleteManufacturerImage = async () => {
   return await deleteImage.mutateAsync({
      id: manufacturer.value.id,
      path: manufacturer.value.image_path,
      table: "manufacturer"
   });
};

const uploadManufacturerImage = async () => {
   return await uploadImage.mutateAsync({
      files: imageUpload.value,
      path: "manufacturer/"
   });
};

const deleteImageHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirmTitle"),
         message: t("app.deleteImage")
      });

      if (confirm) {
         await deleteManufacturerImage();
         snackbar.add({ text: t("app.imageDeleted") });
      }
   } catch {
      snackbar.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};

const formHandler = async () => {
   if (!form.value?.isValid) return;

   const payload: IManufacturerStore = {
      code: manufacturer.value.code,
      translate: [
         {
            language_id: language.value,
            title: manufacturer.value.title,
            content: manufacturer.value.content
         }
      ],
      is_active: manufacturer.value.is_active,
      is_competing: manufacturer.value.is_competing
   };

   try {
      if (imageUpload.value.length) {
         if (manufacturer.value.image_path) {
            await deleteManufacturerImage();
            snackbar.add({ text: t("app.imageDeleted") });
         }

         const upload = await uploadManufacturerImage();
         payload.image_path = upload.data[0];
         imageUpload.value = [];
      }

      if (isCreate.value) {
         await createManufacturer.mutateAsync(payload);
         snackbar.add({ text: t("app.recordCreated") });
      } else {
         await updateManufacturer.mutateAsync({ id: manufacturer.value.id, ...payload });
         snackbar.add({ text: t("app.recordUpdated") });
      }
   } catch (error) {
      snackbar.add({ text: t("app.recordFailed"), color: "error" });
   }
};
</script>
