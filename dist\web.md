# Web

## htaccess

**www to non-www and HTTPS**

```htaccess
RewriteEngine On

# https yönlendirmesi
RewriteCond %{HTTPS} off
RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# www varsa kaldır
RewriteCond %{HTTP_HOST} ^www\.(.+)$ [NC]
RewriteRule ^ https://%1%{REQUEST_URI} [L,R=301]
```

**non-www to www and HTTPS**

```htaccess
RewriteEngine On

# https yönlendirmesi
RewriteCond %{HTTPS} off
RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# www yoksa ekle
RewriteCond %{HTTP_HOST} !^www\. [NC]
RewriteRule ^ https://www.%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

**change root dir**

```htaccess
RewriteEngine On
RewriteBase /

# tarayıcıdan gelen doğrudan /dist/... isteklerini temiz URL'ye yönlendir
RewriteCond %{THE_REQUEST} /dist/([^\s?]*) [NC]
RewriteRule ^ /%1 [L,NE,R=302]

# dist klasöründeki dosyaları kök dizinden erişilebilir yap
RewriteRule ^((?!^dist/).*)$ dist/$1 [L,NC]
```

**redirect**

```htaccess
RewriteEngine On

# /qrlink veya /qrlink/ isteklerini PDF'e yönlendir
RewriteCond %{REQUEST_URI} ^/qrlink/?$ [NC]
RewriteRule ^ https://%{HTTP_HOST}/qrlink/qrlink.pdf [R=301,L]
```

## react refresh 404

SPA projelerinde tarayıcıda `F5` ile sayfa yenilendiğinde `404` sorunu oluşur.

### IIS

Proje klasörüne `web.config` dosyası oluştur.

```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>

<system.webServer>
  <rewrite>
    <rules>
      <rule name="Routes" stopProcessing="true">
        <match url=".*" />
        <conditions logicalGrouping="MatchAll">
          <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
          <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
        </conditions>
        <action type="Rewrite" url="/" />
      </rule>
    </rules>
  </rewrite>
</system.webServer>

</configuration>
```

### Apache

Proje klasörüne `.htaccess` dosyası oluştur.

```htaccess
RewriteEngine on

RewriteCond %{REQUEST_FILENAME} -s [OR]
RewriteCond %{REQUEST_FILENAME} -l [OR]
RewriteCond %{REQUEST_FILENAME} -d

RewriteRule ^.*$ - [NC,L]
RewriteRule ^(.*) index.html [NC,L]
```
