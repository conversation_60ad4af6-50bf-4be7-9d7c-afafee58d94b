<?php

declare(strict_types=1);

use System\Router\Router;
use System\Container\Container;
use App\Core\Middlewares\Swagger;
use App\Modules\Website\Controllers\SwaggerController as Website;
use App\Modules\Inventory\Controllers\SwaggerController as Inventory;

// Requires
require_once __DIR__ . "/App/Config/Constants.php";
require_once __DIR__ . "/vendor/autoload.php";
foreach (glob(__DIR__ . "/System/Helpers/*.php") as $filename) {
   require_once $filename;
}

// Container
$container = new Container();
$container->register();

// Exception
set_exception_handler([$container->get('error'), 'handleException']);
set_error_handler([$container->get('error'), 'handleError']);

// App Config
$config = import_config('defines.app');
import_env($config['env']);

// Router
$router = new Router($container);

// Swagger
$router->prefix('swagger')->middleware([Swagger::class])->group(function () use ($router) {
   $router->get('/', function () {
      require ROOT_DIR . '/Public/swagger/index.html';
   });

   $router->get('/inventory', [Inventory::class, 'json']);
   $router->get('/website', [Website::class, 'json']);

   $router->get('/list', function () {
      header('Content-Type: application/json; charset=UTF-8');
      print(json_encode([
         ['url' => './swagger/inventory', 'name' => 'Inventory'],
         ['url' => './swagger/website', 'name' => 'Website']
      ]));
   });
});

// Routes
foreach (glob(ROOT_DIR . '/' .  $config['routes'] . '/*.php') as $route) {
   require $route;
}
$router->run();
