<?php

declare(strict_types=1);

namespace App\Modules\Inventory\Repositories;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class CompetingRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'competing'
   ) {
      $this->database->prefix('inv_');
   }

   public function findAll(int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_competing.*
            FROM inv_competing
            WHERE inv_competing.deleted_at IS NULL
         ')
         ->execute()
         ->fetchAll();
   }

   public function findOne(int $id, int $lang_id): array|false {
      return $this->database
         ->prepare('SELECT
               inv_competing.*
            FROM inv_competing
            WHERE inv_competing.deleted_at IS NULL
               AND inv_competing.id = :id
         ')
         ->execute([
            'id' => $id,
         ])
         ->fetch();
   }

   public function findCategory(int $competing_id, int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_category.id,
               inv_category_translate.title
            FROM inv_competing_category

            JOIN inv_category ON inv_category.id = inv_competing_category.category_id
               AND inv_category.deleted_at IS NULL
            LEFT JOIN inv_category_translate ON inv_category_translate.category_id = inv_competing_category.category_id
               AND inv_category_translate.language_id = :language_id
            WHERE inv_competing_category.competing_id = :competing_id
         ')
         ->execute([
            'competing_id' => $competing_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();
   }

   public function findManufacturer(int $competing_id, int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_manufacturer.id,
               inv_manufacturer_translate.title
            FROM inv_competing_manufacturer

            JOIN inv_manufacturer ON inv_manufacturer.id = inv_competing_manufacturer.manufacturer_id
               AND inv_manufacturer.deleted_at IS NULL
            LEFT JOIN inv_manufacturer_translate ON inv_manufacturer_translate.manufacturer_id = inv_competing_manufacturer.manufacturer_id
               AND inv_manufacturer_translate.language_id = :language_id
            WHERE inv_competing_manufacturer.competing_id = :competing_id
         ')
         ->execute([
            'competing_id' => $competing_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();
   }

   public function findAttr(int $competing_id, int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_attr.id,
               inv_attr_translate.title
            FROM inv_competing_attr

            JOIN inv_attr ON inv_attr.id = inv_competing_attr.attr_id
               AND inv_attr.deleted_at IS NULL
            LEFT JOIN inv_attr_translate ON inv_attr_translate.attr_id = inv_competing_attr.attr_id
               AND inv_attr_translate.language_id = :language_id
            WHERE inv_competing_attr.competing_id = :competing_id
         ')
         ->execute([
            'competing_id' => $competing_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();
   }

   public function findStandard(int $competing_id, int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_standard.id,
               inv_standard_translate.title,
               inv_competing_standard.value,
               inv_standard.image_path
            FROM inv_competing_standard

            JOIN inv_standard ON inv_standard.id = inv_competing_standard.standard_id
               AND inv_standard.deleted_at IS NULL
            LEFT JOIN inv_standard_translate ON inv_standard_translate.standard_id = inv_competing_standard.standard_id
               AND inv_standard_translate.language_id = :language_id
            WHERE inv_competing_standard.competing_id = :competing_id
         ')
         ->execute([
            'competing_id' => $competing_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();
   }

   public function findProduct(int $competing_id, int $lang_id): array {
      return $this->database
         ->prepare('SELECT
               inv_product.id,
               inv_product_translate.title,
               inv_product_translate.content
            FROM inv_competing_product

            JOIN inv_product ON inv_product.id = inv_competing_product.product_id
               AND inv_product.deleted_at IS NULL
            LEFT JOIN inv_product_translate ON inv_product_translate.product_id = inv_competing_product.product_id
               AND inv_product_translate.language_id = :language_id
            WHERE inv_competing_product.competing_id = :competing_id
         ')
         ->execute([
            'competing_id' => $competing_id,
            'language_id' => $lang_id,
         ])
         ->fetchAll();
   }
}
