(function () {
  'use strict';

  function i18ntrk(args) {
    let chars = ['üÜuU', 'iİıI', 'ğĞgG', 'şŞsS', 'çÇcC', 'öÖoO'];
    if (!args) return '';

    args = args.toLocaleLowerCase();
    chars.filter((char) => {
      let reg = new RegExp('[' + char + ']', 'g');
      args = args.replace(reg, '[' + char + ']');
    });
    return args;
  }

  var install = function (hook, vm) {
    hook.ready(function () {
      // Docsify search plugin'inin doSearch fonksiyonunu override et
      var originalDoSearch = window.doSearch;
      if (originalDoSearch) {
        window.doSearch = function(value) {
          // Önce orijinal arama yap
          originalDoSearch(value);

          // Eğer sonuç yoksa Türkçe karakterlerle tekrar dene
          var $panel = window.Docsify.dom.find('.results-panel');
          if ($panel && $panel.innerHTML.includes('sonuç bulunamadı') && value) {
            var normalizedValue = i18ntrk(value);
            if (normalizedValue !== value) {
              originalDoSearch(normalizedValue);
            }
          }
        };
      }

      // Alternatif: Input event'ini intercept et
      setTimeout(function() {
        var $input = window.Docsify.dom.find('.search input');
        if ($input) {
          var originalHandler = $input.oninput;
          $input.addEventListener('input', function(e) {
            var query = e.target.value.trim();
            if (query) {
              // Türkçe karakterli arama da yap
              setTimeout(function() {
                var $panel = window.Docsify.dom.find('.results-panel');
                if ($panel && $panel.innerHTML.includes('sonuç bulunamadı')) {
                  var normalizedQuery = i18ntrk(query);
                  if (normalizedQuery !== query) {
                    e.target.value = normalizedQuery;
                    e.target.dispatchEvent(new Event('input'));
                    setTimeout(function() {
                      e.target.value = query;
                    }, 100);
                  }
                }
              }, 150);
            }
          });
        }
      }, 1000);
    });
  };

  if (typeof window !== 'undefined' && window.$docsify) {
    window.$docsify.plugins = [].concat(install, window.$docsify.plugins || []);
  }
})();


