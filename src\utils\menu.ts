export const registerMenu = async (): Promise<void> => {
   const appStore = useAppStore();
   appStore.setMenuLoading(true);
   await loadMenu()
      .then((menu) => {
         appStore.setMenu(menu);
      })
      .finally(() => {
         appStore.setMenuLoading(false);
      });
};

export const appMenu: TList[] = [
   {
      itemTitle: "module.inventory",
      itemProps: {
         prependIcon: "$inventory",
         value: "Inventory"
      }
   },
   {
      itemTitle: "module.website",
      itemProps: {
         prependIcon: "$website",
         value: "Website"
      }
   },
   {
      itemTitle: "module.dealer",
      itemProps: {
         prependIcon: "$dealer",
         value: "Dealer"
      }
   },
   {
      itemTitle: "module.customer",
      itemProps: {
         prependIcon: "$crm",
         value: "Customer"
      }
   },
   {
      itemTitle: "module.warehouse",
      itemProps: {
         prependIcon: "$warehouse",
         value: "Warehouse"
      }
   },
   {
      itemTitle: "module.employee",
      itemProps: {
         prependIcon: "$employee",
         value: "Employee"
      }
   },
   {
      itemTitle: "module.equipment",
      itemProps: {
         prependIcon: "$devices",
         value: "Devices"
      }
   },
   {
      itemTitle: "module.socialMedia",
      itemProps: {
         prependIcon: "$socialMedia",
         value: "Social"
      }
   }
];
