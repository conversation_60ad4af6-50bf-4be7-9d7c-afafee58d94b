{"name": "vue-starterkit", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "prettier": "prettier --write src/", "tsc": "vue-tsc"}, "browserslist": ["> 1%", "last 2 versions", "not op_mini all", "not ie>0"], "dependencies": {"@mdi/js": "^7.4.47", "@phosphor-icons/vue": "^2.2.1", "@tabler/icons": "^3.34.1", "@tabler/icons-vue": "^3.34.1", "@tanstack/vue-query": "^5.83.1", "axios": "^1.11.0", "country-flag-icons": "^1.5.19", "maska": "^3.2.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.5.0", "vue": "^3.5.18", "vue-i18n": "^11.1.11", "vue-router": "^4.5.1", "vuetify": "^3.9.5"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^6.0.8", "@tailwindcss/postcss": "^4.1.11", "@tanstack/vue-query-devtools": "^5.84.0", "@types/node": "24.2.1", "@vitejs/plugin-vue": "6.0.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-organize-attributes": "^1.0.0", "prettier-plugin-tailwindcss": "^0.6.14", "sass-embedded": "^1.90.0", "tailwindcss": "^4.1.11", "ts-node": "^10.9.2", "typescript": "^5.9.2", "unplugin-auto-import": "20.0.0", "vite": "7.1.2", "vite-plugin-vuetify": "^2.1.2", "vite-plugin-webfont-dl": "^3.11.1", "vue-tsc": "3.0.5"}}